{"description": "The CodeIgniter framework", "name": "codeigniter/framework", "type": "project", "homepage": "https://codeigniter.com", "license": "MIT", "support": {"forum": "https://forum.codeigniter.com/", "wiki": "https://github.com/bcit-ci/CodeIgniter/wiki", "slack": "https://codeigniterchat.slack.com", "source": "https://github.com/bcit-ci/CodeIgniter"}, "require": {"php": "^8.1", "nikic/php-parser": "^4.17", "aws/aws-sdk-php": "^3.339", "vlucas/phpdotenv": "^5.6", "predis/predis": "^3.0"}, "scripts": {"post-install-cmd": ["php -r \"file_exists('.env') || copy('.env.example', '.env');\""]}, "suggest": {"paragonie/random_compat": "Provides better randomness in PHP 5.x"}, "require-dev": {"mikey179/vfsstream": "1.6.*", "phpunit/phpunit": "^9.6", "vimeo/psalm": "^5.26.1", "kenjis/ci-phpunit-test": "^3.0"}, "config": {"platform": {"php": "8.1.31"}}, "autoload": {"psr-4": {"Storage\\": "application/libraries/Storage/"}}}