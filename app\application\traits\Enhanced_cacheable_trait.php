<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Enhanced Cacheable Trait
 *
 * Trait mejorado que proporciona funcionalidad de caché avanzada
 * Compatible con cualquier modelo, no solo los que extienden Crud_model
 * Usa la capa de abstracción para gestión automática de drivers
 *
 * @package    CodeIgniter
 * @subpackage Traits
 * @category   Cache
 * <AUTHOR> Pro Team
 * @version    2.0.0
 */
trait Enhanced_cacheable_trait
{
    /**
     * Tiempo de caché para los datos (en segundos)
     * @var int|null
     */
    protected $cache_time = 300; // 5 minutos por defecto

    /**
     * Tiempo de caché para las claves de versión (en segundos)
     * @var int|null
     */
    protected $version_cache_time = null; // null = nunca expira

    /**
     * Nombre de la tabla para la que se gestiona la caché
     * @var string
     */
    protected $cache_table;

    /**
     * Instancia de la capa de abstracción de caché
     * @var Cache_abstraction_layer
     */
    protected $cache_layer;

    /**
     * Prefijo personalizado para las claves de caché
     * @var string
     */
    protected $cache_prefix = '';

    /**
     * Habilitar/deshabilitar caché para este modelo
     * @var bool
     */
    protected $cache_enabled = true;

    /**
     * Inicializa el sistema de caché
     *
     * @param string $table Nombre de la tabla
     * @param array $options Opciones adicionales
     * @return void
     */
    protected function init_enhanced_cache($table, $options = [])
    {
        $this->cache_table = $table;

        // Aplicar opciones
        if (isset($options['cache_time'])) {
            $this->cache_time = $options['cache_time'];
        }
        if (isset($options['version_cache_time'])) {
            $this->version_cache_time = $options['version_cache_time'];
        }
        if (isset($options['cache_prefix'])) {
            $this->cache_prefix = $options['cache_prefix'];
        }
        if (isset($options['cache_enabled'])) {
            $this->cache_enabled = $options['cache_enabled'];
        }

        // Obtener la instancia de CI
        $CI = &get_instance();

        // Cargar la capa de abstracción de caché
        if (!isset($this->cache_layer)) {
            $CI->load->library('Cache_abstraction_layer');
            $this->cache_layer = $CI->cache_abstraction_layer;
        }

        log_message('info', "Enhanced Cache initialized for table: {$this->cache_table}");
    }

    /**
     * Configura el tiempo de caché para este modelo
     *
     * @param int|null $seconds Cache duration in seconds (null = never expire)
     * @return $this For method chaining
     */
    public function set_cache_time($seconds)
    {
        $this->cache_time = ($seconds === null) ? null : (int) $seconds;
        return $this;
    }

    /**
     * Configura el tiempo de caché para las claves de versión
     *
     * @param int|null $seconds Cache duration in seconds (null = never expire)
     * @return $this For method chaining
     */
    public function set_version_cache_time($seconds)
    {
        $this->version_cache_time = ($seconds === null) ? null : (int) $seconds;
        return $this;
    }

    /**
     * Habilita o deshabilita el caché para este modelo
     *
     * @param bool $enabled
     * @return $this For method chaining
     */
    public function set_cache_enabled($enabled)
    {
        $this->cache_enabled = (bool) $enabled;
        return $this;
    }

    /**
     * Genera una clave de caché consistente
     *
     * @param string $function_name Function name
     * @param array $params Parameters
     * @param bool $include_version Include version in key
     * @return string Cache key
     */
    protected function generate_enhanced_cache_key($function_name, $params = [], $include_version = true)
    {
        if (!$this->cache_enabled) {
            return null;
        }

        ksort($params);
        $params_str = json_encode($params);
        $hashed_params = md5($params_str);
        
        $key_parts = [];
        
        if ($this->cache_prefix) {
            $key_parts[] = $this->cache_prefix;
        }
        
        $key_parts[] = $this->cache_table;
        
        if ($include_version) {
            $version = $this->get_enhanced_cache_version();
            $key_parts[] = "v{$version}";
        }
        
        $key_parts[] = "{$function_name}_{$hashed_params}";
        
        return implode(':', $key_parts);
    }

    /**
     * Obtiene datos con caché usando la capa de abstracción
     *
     * @param string $cache_key Cache key
     * @param callable $query_callback Callback function to get data if not in cache
     * @param int|null $custom_ttl Custom TTL for this operation
     * @return mixed Cached data or fresh data
     */
    protected function get_enhanced_cached_data($cache_key, $query_callback, $custom_ttl = null)
    {
        if (!$this->cache_enabled || !$cache_key) {
            return $query_callback();
        }

        // Intentar obtener del caché
        $cached_data = $this->cache_layer->get($cache_key);
        if ($cached_data !== FALSE) {
            log_message('debug', "Enhanced Cache hit for key: {$cache_key}");
            return $cached_data;
        }

        // Si no está en caché, generar datos
        $data = $query_callback();

        // Guardar en caché
        $ttl = $custom_ttl !== null ? $custom_ttl : $this->cache_time;
        $success = $this->save_enhanced_cache_data($cache_key, $data, $ttl);

        if ($success) {
            log_message('debug', "Enhanced Cache saved for key: {$cache_key}");
        } else {
            log_message('error', "Enhanced Cache save failed for key: {$cache_key}");
        }

        return $data;
    }

    /**
     * Guarda datos en caché con manejo de TTL
     *
     * @param string $key Cache key
     * @param mixed $data Data to save
     * @param int|null $ttl Time to live in seconds (null = never expire)
     * @return bool Success or failure
     */
    protected function save_enhanced_cache_data($key, $data, $ttl)
    {
        if (!$this->cache_enabled) {
            return false;
        }

        // Si TTL es null, usamos un valor muy grande (10 años)
        if ($ttl === null) {
            $ttl = 315360000; // 10 años en segundos
        } else {
            // Asegurarnos de que TTL sea al menos 1 segundo
            $ttl = max(1, (int) $ttl);
        }

        return $this->cache_layer->save($key, $data, $ttl);
    }

    /**
     * Elimina una clave específica del caché
     *
     * @param string $cache_key Cache key to clear
     * @return bool Success or failure
     */
    protected function clear_enhanced_cache($cache_key)
    {
        if (!$this->cache_enabled) {
            return false;
        }

        return $this->cache_layer->delete($cache_key);
    }

    /**
     * Obtiene la versión actual del caché para esta tabla
     *
     * @return int Current cache version
     */
    protected function get_enhanced_cache_version()
    {
        if (!$this->cache_enabled) {
            return 1;
        }

        $version_key = "{$this->cache_table}:version";
        $version = $this->cache_layer->get($version_key);
        
        if ($version === FALSE) {
            // Si no existe, la inicializamos a 1
            $this->save_enhanced_cache_data($version_key, 1, $this->version_cache_time);
            return 1;
        }
        
        return (int) $version;
    }

    /**
     * Incrementa la versión del caché para esta tabla
     *
     * @return int New cache version
     */
    protected function bump_enhanced_cache_version()
    {
        if (!$this->cache_enabled) {
            return 1;
        }

        $version_key = "{$this->cache_table}:version";

        // Verificar si la clave existe
        $current_version = $this->cache_layer->get($version_key);

        if ($current_version === FALSE) {
            // Si no existe, inicializamos a 1
            $this->save_enhanced_cache_data($version_key, 1, $this->version_cache_time);
            return 1;
        }

        // Usar el método increment() de la capa de abstracción
        $new_version = $this->cache_layer->increment($version_key, 1);

        // Si por alguna razón increment() falla, usamos el fallback
        if ($new_version === FALSE) {
            $new_version = (int)$current_version + 1;
            $this->save_enhanced_cache_data($version_key, $new_version, $this->version_cache_time);
        }

        log_message('info', "Enhanced Cache version bumped for table '{$this->cache_table}': {$new_version}");
        return $new_version;
    }

    /**
     * Limpia todo el caché relacionado con esta tabla
     *
     * @return bool Success or failure
     */
    protected function flush_enhanced_cache()
    {
        if (!$this->cache_enabled) {
            return false;
        }

        // Incrementar la versión invalida automáticamente todas las claves
        $this->bump_enhanced_cache_version();
        
        log_message('info', "Enhanced Cache flushed for table: {$this->cache_table}");
        return true;
    }

    /**
     * Crea un objeto proxy que emula CI_DB_result
     * CRÍTICO: Mantiene compatibilidad con consultas de base de datos
     *
     * @param array $cached_data
     * @return object Proxy object that behaves like CI_DB_result
     */
    protected function create_enhanced_db_result_proxy($cached_data)
    {
        return new class($cached_data) {
            private $cached_data;

            public function __construct($cached_data)
            {
                $this->cached_data = is_array($cached_data) ? $cached_data : [];
            }

            public function result()
            {
                return $this->cached_data;
            }

            public function result_array()
            {
                $array_result = [];
                foreach ($this->cached_data as $object) {
                    if (is_object($object)) {
                        $array_result[] = (array) $object;
                    } elseif (is_array($object)) {
                        $array_result[] = $object;
                    } else {
                        $array_result[] = (array) $object;
                    }
                }
                return $array_result;
            }

            public function row($n = 0)
            {
                return isset($this->cached_data[$n]) ? $this->cached_data[$n] : null;
            }

            public function row_array($n = 0)
            {
                $row = $this->row($n);
                return $row ? (array) $row : null;
            }

            public function num_rows()
            {
                return is_array($this->cached_data) ? count($this->cached_data) : 0;
            }

            public function num_fields()
            {
                if ($this->num_rows() > 0) {
                    $first_row = $this->row(0);
                    return is_object($first_row) ? count(get_object_vars($first_row)) : 
                           (is_array($first_row) ? count($first_row) : 0);
                }
                return 0;
            }

            public function field_data()
            {
                // Simular field_data para compatibilidad
                $fields = [];
                if ($this->num_rows() > 0) {
                    $first_row = $this->row(0);
                    if (is_object($first_row)) {
                        foreach (get_object_vars($first_row) as $field => $value) {
                            $fields[] = (object) [
                                'name' => $field,
                                'type' => gettype($value),
                                'max_length' => is_string($value) ? strlen($value) : 0
                            ];
                        }
                    }
                }
                return $fields;
            }

            public function list_fields()
            {
                $fields = [];
                if ($this->num_rows() > 0) {
                    $first_row = $this->row(0);
                    if (is_object($first_row)) {
                        $fields = array_keys(get_object_vars($first_row));
                    } elseif (is_array($first_row)) {
                        $fields = array_keys($first_row);
                    }
                }
                return $fields;
            }

            public function free_result()
            {
                $this->cached_data = [];
                return true;
            }
        };
    }

    /**
     * Obtiene información del estado del caché
     *
     * @return array Cache status information
     */
    public function get_enhanced_cache_info()
    {
        return [
            'table' => $this->cache_table,
            'enabled' => $this->cache_enabled,
            'cache_time' => $this->cache_time,
            'version_cache_time' => $this->version_cache_time,
            'current_version' => $this->get_enhanced_cache_version(),
            'prefix' => $this->cache_prefix,
            'driver_info' => $this->cache_layer ? $this->cache_layer->get_driver_info() : null
        ];
    }

    /**
     * Cambia el driver de caché dinámicamente
     *
     * @param string $driver_type
     * @return bool Success or failure
     */
    public function switch_cache_driver($driver_type)
    {
        if ($this->cache_layer) {
            return $this->cache_layer->switch_driver($driver_type);
        }
        return false;
    }
}
