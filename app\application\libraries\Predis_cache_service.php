<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Predis Cache Service Library
 *
 * Servicio de caché usando Predis como reemplazo del driver nativo de Redis de CodeIgniter.
 * Mantiene compatibilidad total con la interfaz existente del sistema de caché.
 *
 * @package    CodeIgniter
 * @subpackage Libraries
 * @category   Cache
 * <AUTHOR> Pro Team
 * @version    1.0.0
 */
class Predis_cache_service
{
    /**
     * Instancia de CodeIgniter
     * @var CI_Controller
     */
    protected $CI;

    /**
     * Cliente Predis
     * @var Predis\Client
     */
    protected $predis;

    /**
     * Configuración de Redis
     * @var array
     */
    protected $config;

    /**
     * Prefijo para las claves de caché
     * @var string
     */
    protected $prefix = 'ci_cache:';

    /**
     * Conjunto para rastrear claves serializadas
     * @var string
     */
    protected $serialized_set = '_ci_redis_serialized';

    /**
     * Cache local para claves serializadas
     * @var array
     */
    protected $_serialized = array();

    /**
     * Estado de conexión
     * @var bool
     */
    protected $is_connected = false;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->CI =& get_instance();
        $this->load_config();
        $this->init_predis();
        log_message('info', 'Predis Cache Service Class Initialized');
    }

    /**
     * Carga la configuración de Redis
     */
    protected function load_config()
    {
        // Cargar configuración desde cache.php
        $this->CI->config->load('cache', TRUE);
        $cache_config = $this->CI->config->item('cache');
        
        // Cargar configuración específica de Redis
        $this->CI->config->load('redis', TRUE);
        $redis_config = $this->CI->config->item('redis');

        // Configuración por defecto
        $default_config = array(
            'scheme' => 'tcp',
            'host' => '127.0.0.1',
            'port' => 6379,
            'password' => null,
            'timeout' => 0,
            'database' => 0
        );

        // Combinar configuraciones
        if (is_array($redis_config)) {
            $this->config = array_merge($default_config, $redis_config);
        } else {
            $this->config = $default_config;
        }

        // Mapear socket_type a scheme para Predis
        if (isset($this->config['socket_type'])) {
            $this->config['scheme'] = $this->config['socket_type'];
            unset($this->config['socket_type']);
        }
    }

    /**
     * Inicializa el cliente Predis
     */
    protected function init_predis()
    {
        try {
            // Verificar que Predis esté disponible
            if (!class_exists('Predis\Client')) {
                // Intentar cargar desde vendor
                $vendor_autoload = APPPATH . '../vendor/autoload.php';
                if (file_exists($vendor_autoload)) {
                    require_once $vendor_autoload;
                }

                if (!class_exists('Predis\Client')) {
                    log_message('error', 'Predis Cache: Predis library not found');
                    return;
                }
            }

            // Optimizaciones para Predis
            $optimized_config = $this->config;

            // Configurar opciones de conexión optimizadas
            $optimized_config['options'] = [
                'connections' => [
                    'tcp' => 'Predis\Connection\StreamConnection',
                ],
                'exceptions' => true,
                'prefix' => $this->prefix,
            ];

            // Crear cliente Predis con configuración optimizada
            $this->predis = new Predis\Client($optimized_config);

            // Probar conexión con timeout
            $this->predis->ping();
            $this->is_connected = true;

            log_message('info', 'Predis Cache: Successfully connected to Redis server with optimizations');

        } catch (Exception $e) {
            log_message('error', 'Predis Cache: Connection failed - ' . $e->getMessage());
            $this->is_connected = false;
        }
    }

    /**
     * Verifica si el servicio está disponible
     * 
     * @return bool
     */
    public function is_supported()
    {
        return $this->is_connected && class_exists('Predis\Client');
    }

    /**
     * Obtiene un valor del caché
     *
     * @param string $key Clave del caché
     * @return mixed Valor del caché o FALSE si no existe
     */
    public function get($key)
    {
        if (!$this->is_connected) {
            return FALSE;
        }

        try {
            $key = $this->prefix . $key;
            $value = $this->predis->get($key);
            
            if ($value === null) {
                return FALSE;
            }

            // Verificar si la clave está marcada como serializada
            if ($this->predis->sismember($this->serialized_set, $key)) {
                $this->_serialized[$key] = TRUE;
                return unserialize($value);
            }

            return $value;
            
        } catch (Exception $e) {
            log_message('error', 'Predis Cache: Get failed for key "' . $key . '" - ' . $e->getMessage());
            return FALSE;
        }
    }

    /**
     * Guarda un valor en el caché
     *
     * @param string $key Clave del caché
     * @param mixed $value Valor a guardar
     * @param int $ttl Tiempo de vida en segundos
     * @return bool TRUE en éxito, FALSE en fallo
     */
    public function save($key, $value, $ttl = 60)
    {
        if (!$this->is_connected) {
            return FALSE;
        }

        try {
            $key = $this->prefix . $key;
            
            // Manejar serialización para arrays y objetos
            if (is_array($value) || is_object($value)) {
                if (!$this->predis->sismember($this->serialized_set, $key)) {
                    $this->predis->sadd($this->serialized_set, $key);
                }
                $this->_serialized[$key] = TRUE;
                $value = serialize($value);
            } else {
                // Remover de conjunto de serializados si no es array/objeto
                $this->predis->srem($this->serialized_set, $key);
                unset($this->_serialized[$key]);
            }

            // Guardar con TTL
            if ($ttl > 0) {
                return $this->predis->setex($key, $ttl, $value) === 'OK';
            } else {
                return $this->predis->set($key, $value) === 'OK';
            }
            
        } catch (Exception $e) {
            log_message('error', 'Predis Cache: Save failed for key "' . $key . '" - ' . $e->getMessage());
            return FALSE;
        }
    }

    /**
     * Elimina una clave del caché
     *
     * @param string $key Clave a eliminar
     * @return bool TRUE en éxito, FALSE en fallo
     */
    public function delete($key)
    {
        if (!$this->is_connected) {
            return FALSE;
        }

        try {
            $key = $this->prefix . $key;
            
            // Remover de conjunto de serializados
            $this->predis->srem($this->serialized_set, $key);
            unset($this->_serialized[$key]);
            
            return $this->predis->del($key) > 0;
            
        } catch (Exception $e) {
            log_message('error', 'Predis Cache: Delete failed for key "' . $key . '" - ' . $e->getMessage());
            return FALSE;
        }
    }

    /**
     * Incrementa un valor numérico en el caché
     *
     * @param string $key Clave a incrementar
     * @param int $offset Cantidad a incrementar
     * @return mixed Nuevo valor o FALSE en fallo
     */
    public function increment($key, $offset = 1)
    {
        if (!$this->is_connected) {
            return FALSE;
        }

        try {
            $key = $this->prefix . $key;
            return $this->predis->incrby($key, $offset);
            
        } catch (Exception $e) {
            log_message('error', 'Predis Cache: Increment failed for key "' . $key . '" - ' . $e->getMessage());
            return FALSE;
        }
    }

    /**
     * Decrementa un valor numérico en el caché
     *
     * @param string $key Clave a decrementar
     * @param int $offset Cantidad a decrementar
     * @return mixed Nuevo valor o FALSE en fallo
     */
    public function decrement($key, $offset = 1)
    {
        if (!$this->is_connected) {
            return FALSE;
        }

        try {
            $key = $this->prefix . $key;
            return $this->predis->decrby($key, $offset);
            
        } catch (Exception $e) {
            log_message('error', 'Predis Cache: Decrement failed for key "' . $key . '" - ' . $e->getMessage());
            return FALSE;
        }
    }

    /**
     * Limpia todo el caché
     *
     * @return bool TRUE en éxito, FALSE en fallo
     */
    public function clean()
    {
        if (!$this->is_connected) {
            return FALSE;
        }

        try {
            return $this->predis->flushdb() === 'OK';
            
        } catch (Exception $e) {
            log_message('error', 'Predis Cache: Clean failed - ' . $e->getMessage());
            return FALSE;
        }
    }

    /**
     * Obtiene información de una clave de caché
     *
     * @param string $key Clave del caché
     * @return array|FALSE Metadatos o FALSE si no existe
     */
    public function get_metadata($key)
    {
        if (!$this->is_connected) {
            return FALSE;
        }

        try {
            $key = $this->prefix . $key;
            
            if (!$this->predis->exists($key)) {
                return FALSE;
            }

            $ttl = $this->predis->ttl($key);
            
            return array(
                'expire' => $ttl > 0 ? time() + $ttl : 0,
                'mtime' => time(),
                'data' => $this->get(str_replace($this->prefix, '', $key))
            );
            
        } catch (Exception $e) {
            log_message('error', 'Predis Cache: Get metadata failed for key "' . $key . '" - ' . $e->getMessage());
            return FALSE;
        }
    }

    /**
     * Verifica si una clave existe en el caché
     *
     * @param string $key Clave a verificar
     * @return bool TRUE si existe, FALSE si no
     */
    public function exists($key)
    {
        if (!$this->is_connected) {
            return FALSE;
        }

        try {
            $key = $this->prefix . $key;
            return $this->predis->exists($key) > 0;
            
        } catch (Exception $e) {
            log_message('error', 'Predis Cache: Exists check failed for key "' . $key . '" - ' . $e->getMessage());
            return FALSE;
        }
    }

    /**
     * Obtiene el cliente Predis para operaciones avanzadas
     *
     * @return Predis\Client|null
     */
    public function get_predis_client()
    {
        return $this->predis;
    }

    /**
     * Obtiene estadísticas de conexión
     *
     * @return array
     */
    public function get_stats()
    {
        if (!$this->is_connected) {
            return array('connected' => false);
        }

        try {
            $info = $this->predis->info();
            $stats = array(
                'connected' => true,
                'redis_version' => isset($info['redis_version']) ? $info['redis_version'] : 'unknown',
                'used_memory' => isset($info['used_memory_human']) ? $info['used_memory_human'] : 'unknown',
                'used_memory_peak' => isset($info['used_memory_peak_human']) ? $info['used_memory_peak_human'] : 'unknown',
                'connected_clients' => isset($info['connected_clients']) ? $info['connected_clients'] : 'unknown',
                'total_commands_processed' => isset($info['total_commands_processed']) ? $info['total_commands_processed'] : 'unknown',
                'keyspace_hits' => isset($info['keyspace_hits']) ? $info['keyspace_hits'] : 'unknown',
                'keyspace_misses' => isset($info['keyspace_misses']) ? $info['keyspace_misses'] : 'unknown',
                'uptime_in_seconds' => isset($info['uptime_in_seconds']) ? $info['uptime_in_seconds'] : 'unknown'
            );

            // Calcular hit rate si está disponible
            if (isset($info['keyspace_hits']) && isset($info['keyspace_misses'])) {
                $hits = (int)$info['keyspace_hits'];
                $misses = (int)$info['keyspace_misses'];
                $total = $hits + $misses;
                $stats['hit_rate'] = $total > 0 ? round(($hits / $total) * 100, 2) . '%' : '0%';
            }

            return $stats;
        } catch (Exception $e) {
            return array('connected' => false, 'error' => $e->getMessage());
        }
    }
}
