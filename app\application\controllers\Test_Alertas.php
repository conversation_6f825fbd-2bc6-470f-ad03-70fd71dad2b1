<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Test_Alertas Controller
 *
 * Este controlador proporciona métodos para probar y verificar el sistema de alertas,
 * incluyendo los hooks de rendimiento y error, así como el procesamiento de alertas.
 *
 * @package     CodeIgniter
 * @subpackage  Controllers
 * @category    Tests
 * <AUTHOR>
 */
class Test_Alertas extends CI_Controller {

    protected $alertProcessor;

    public function __construct()
    {
        parent::__construct();
        // Cargar la biblioteca AlertProcessor_library
        $this->load->library('AlertProcessor_library');
    }

    /**
     * Página de índice
     *
     * Muestra los enlaces a las diferentes pruebas disponibles.
     */
    public function index()
    {
        echo "<h1>Pruebas del Sistema de Alertas</h1>";
        echo "<ul>";
        echo "<li><a href='" . site_url('Test_Alertas/simular_lentitud') . "'>Simular Lentitud</a></li>";
        echo "<li><a href='" . site_url('Test_Alertas/simular_error') . "'>Simular Error</a></li>";
        echo "<li><a href='" . site_url('Test_Alertas/procesar_alertas') . "'>Procesar Alertas</a></li>";
        echo "</ul>";
    }

    /**
    * Simula una solicitud lenta
    *
    * Este método simula una solicitud que tarda más de 10 segundos en completarse,
    * lo que debería activar el hook de rendimiento.
    */
    public function simular_lentitud()
    {
        // Simular pre_system hook
        $this->benchmark->mark('total_execution_start');

        // Simular una operación lenta
        sleep(11);  // Dormir por 11 segundos para superar el límite de 10 segundos

        // Simular post_system hook
        $this->benchmark->mark('total_execution_end');

        $execution_time = $this->benchmark->elapsed_time('total_execution_start', 'total_execution_end');

        // Llamar manualmente al hook de rendimiento
        $performance_hook = $this->config->item('post_system');
        if (is_array($performance_hook)) {
            foreach ($performance_hook as $hook) {
                if ($hook['class'] === 'PerformanceHook') {
                    $this->load->library($hook['class']);
                    $this->{strtolower($hook['class'])}->post_system();
                    break;
                }
            }
        }

        echo "<h2>Simulación de Lentitud Completada</h2>";
        echo "<p>Se ha simulado una solicitud que tardó {$execution_time} segundos en completarse.</p>";

        $this->mostrar_cola_alertas('performance');
    }

    /**
     * Simula un error 500
     *
     * Este método simula un error 500, lo que debería activar el hook de error.
     */
    public function simular_error()
    {
        // Forzar un error 500
        $this->output->set_status_header(500);

        // Llamar manualmente al hook de error desde post_system
        $post_system_hooks = $this->config->item('post_system');
        if (is_array($post_system_hooks)) {
            foreach ($post_system_hooks as $hook) {
                if ($hook['class'] === 'ErrorHook') {
                    $this->load->library($hook['class']);
                    $this->{$hook['class']}->handle_error();  // No es necesario strtolower
                    break;
                }
            }
        }

        echo "<h2>Simulación de Error Completada</h2>";
        echo "<p>Se ha simulado un error 500.</p>";

        $this->mostrar_cola_alertas('error');
    }

    /**
     * Procesa las alertas en la cola
     *
     * Este método procesa todas las alertas actualmente en la cola Redis.
     */
    public function procesar_alertas()
    {
        $response = $this->alertprocessor_library->processAlerts();

        if ($this->input->is_ajax_request()) {
            echo json_encode($response);
        } else {
            echo "<h2>Procesamiento de Alertas Completado</h2>";
            echo "<p>{$response['message']}</p>";
        }

        $this->mostrar_cola_alertas();
    }

    /**
     * Muestra las alertas en la cola Redis
     *
     * @param string|null $expected_type Tipo de alerta esperado (opcional)
     */
    private function mostrar_cola_alertas($expected_type = null)
    {
        // Usar la capa de abstracción de caché v2.0
        if (!isset($this->cache_abstraction_layer)) {
            $this->load->library('Cache_abstraction_layer');
        }
        $alerts_json = $this->cache_abstraction_layer->get('alerts_queue');
        $alerts = $alerts_json ? json_decode($alerts_json, true) : [];

        echo "<h3>Alertas en la Cola:</h3>";
        if (empty($alerts)) {
            echo "<p>No hay alertas en la cola.</p>";
        } else {
            echo "<ul>";
            foreach ($alerts as $alert) {
                echo "<li>";
                echo "Tipo: " . $alert['type'] . ", ";
                echo "URL: " . $alert['url'] . ", ";
                if (isset($alert['execution_time'])) {
                    echo "Tiempo de ejecución: " . $alert['execution_time'] . " segundos, ";
                }
                echo "Fecha: " . $alert['datetime'];
                echo "</li>";
            }
            echo "</ul>";
        }

        if ($expected_type) {
            $last_alert = end($alerts);
            if ($last_alert) {
                if ($last_alert['type'] === $expected_type) {
                    echo "<p style='color: green;'>La alerta de tipo '{$expected_type}' se agregó correctamente a la cola.</p>";
                } else {
                    echo "<p style='color: red;'>Error: Se esperaba una alerta de tipo '{$expected_type}', pero se encontró una de tipo '{$last_alert['type']}'.</p>";
                }
            } else {
                echo "<p style='color: red;'>Error: No se agregó ninguna alerta a la cola.</p>";
            }
        }
    }

    /**
     * Verifica la configuración general del sistema de alertas
     */
    public function verificar_configuracion()
    {
        echo "<h2>Verificación de Configuración</h2>";

        $this->verificar_hooks();
        $this->verificar_redis();
        $this->verificar_config_alertas();
        $this->verificar_funcion_correo();
    }

    /**
     * Verifica la configuración de los hooks
     */
    private function verificar_hooks()
    {
        echo "<h3>Verificación de Hooks:</h3>";
        $hooks = $this->config->item('hooks');
        if ($hooks && is_array($hooks)) {
            $performance_hook_found = false;
            $error_hook_found = false;
            foreach ($hooks as $hook_point => $hook_array) {
                if (is_array($hook_array)) {
                    foreach ($hook_array as $hook) {
                        if ($hook['class'] === 'PerformanceHook') {
                            $performance_hook_found = true;
                        }
                        if ($hook['class'] === 'ErrorHook') {
                            $error_hook_found = true;
                        }
                    }
                }
            }
            if ($performance_hook_found && $error_hook_found) {
                echo "<p style='color: green;'>Los hooks de rendimiento y error están configurados correctamente.</p>";
            } else {
                if (!$performance_hook_found) {
                    echo "<p style='color: red;'>Error: No se encontró la configuración del hook de rendimiento.</p>";
                }
                if (!$error_hook_found) {
                    echo "<p style='color: red;'>Error: No se encontró la configuración del hook de error.</p>";
                }
            }
        } else {
            echo "<p style='color: red;'>Error: No se encontró la configuración de hooks o no es válida.</p>";
        }
    }

    /**
     * Verifica la configuración de Redis
     */
    private function verificar_redis()
    {
        echo "<h3>Verificación de Redis:</h3>";
        try {
            if (!isset($this->cache_abstraction_layer)) {
                $this->load->library('Cache_abstraction_layer');
            }

            $driver_info = $this->cache_abstraction_layer->get_driver_info();
            if ($driver_info['initialized']) {
                echo "<p style='color: green;'>Cache está configurado y funcionando correctamente.</p>";
                echo "<p>Driver activo: " . $driver_info['type'] . "</p>";
            } else {
                echo "<p style='color: red;'>Error: Cache no está soportado o configurado correctamente.</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>Error al conectar con Cache: " . $e->getMessage() . "</p>";
        }
    }

    /**
     * Verifica la configuración de alertas
     */
    private function verificar_config_alertas()
    {
        echo "<h3>Verificación de Configuración de Alertas:</h3>";
        if ($this->config->item('performance_alert_recipients') && $this->config->item('error_alert_recipients')) {
            echo "<p style='color: green;'>La configuración de alertas está presente.</p>";
        } else {
            echo "<p style='color: red;'>Error: Falta la configuración de destinatarios de alertas en el archivo alerts.php.</p>";
        }
    }

    /**
     * Verifica la función de envío de correo
     */
    private function verificar_funcion_correo()
    {
        echo "<h3>Verificación de Función de Envío de Correo:</h3>";
        if (function_exists('send_app_mail')) {
            echo "<p style='color: green;'>La función send_app_mail está definida.</p>";
        } else {
            echo "<p style='color: red;'>Error: La función send_app_mail no está definida. Asegúrate de que esté implementada o reemplázala por tu método de envío de correo.</p>";
        }
    }
}
