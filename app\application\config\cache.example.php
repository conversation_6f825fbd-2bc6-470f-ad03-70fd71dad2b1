<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Archivo de configuración de ejemplo para el sistema de caché
 * 
 * Este archivo muestra la configuración recomendada para usar Predis
 * como reemplazo del driver nativo de Redis en CodeIgniter 3.
 * 
 * Para usar este archivo:
 * 1. Copiar a cache.php
 * 2. Ajustar las configuraciones según el entorno
 * 3. Asegurar que Predis esté instalado via Composer
 */

// ============================================================================
// CONFIGURACIÓN PRINCIPAL DEL DRIVER DE CACHÉ
// ============================================================================

// Driver principal de caché - Redis es recomendado para producción
$config['cache_driver'] = 'redis';

// Driver de respaldo en caso de fallo del principal
$config['cache_backup'] = 'file';

// ============================================================================
// CONFIGURACIÓN DE PREDIS
// ============================================================================

// Habilitar Predis como reemplazo del driver nativo de Redis
// TRUE = usar Predis (recomendado)
// FALSE = usar driver nativo de CodeIgniter
$config['use_predis_cache'] = TRUE;

// ============================================================================
// CONFIGURACIÓN ESPECÍFICA DE REDIS
// ============================================================================

$config['redis'] = array(
    // Tipo de socket: 'tcp' para conexiones de red, 'unix' para sockets Unix
    'socket_type' => 'tcp',
    
    // Host del servidor Redis
    'host' => '127.0.0.1',
    
    // Puerto del servidor Redis (por defecto 6379)
    'port' => 6379,
    
    // Contraseña de Redis (dejar null si no hay contraseña)
    'password' => null,
    
    // Timeout de conexión en segundos (0 = sin timeout)
    'timeout' => 5,
    
    // Base de datos Redis a usar (0-15, por defecto 0)
    'database' => 0,
    
    // Configuraciones adicionales para Predis
    'options' => array(
        // Prefijo para todas las claves de caché
        'prefix' => 'ci_cache:',
        
        // Habilitar compresión (requiere extensión zlib)
        'compression' => false,
        
        // Configuración de conexión persistente
        'persistent' => false,
        
        // Configuración de cluster (para múltiples servidores Redis)
        'cluster' => false,
    )
);

// ============================================================================
// CONFIGURACIÓN DE TIEMPOS DE CACHÉ
// ============================================================================

// Tiempo de vida por defecto de los datos en caché (en segundos)
$config['cache_expiration'] = 3600; // 1 hora

// Configuraciones específicas por tipo de dato
$config['cache_times'] = array(
    // Datos de usuario (sesiones, perfiles)
    'user_data' => 1800,        // 30 minutos
    
    // Datos de configuración del sistema
    'system_config' => 86400,   // 24 horas
    
    // Resultados de consultas complejas
    'query_results' => 3600,    // 1 hora
    
    // Datos de navegación y menús
    'navigation' => 7200,       // 2 horas
    
    // Datos estáticos (raramente cambian)
    'static_data' => 604800,    // 1 semana
);

// ============================================================================
// CONFIGURACIÓN DE ENTORNOS
// ============================================================================

// Configuraciones específicas por entorno
if (ENVIRONMENT === 'development') {
    // En desarrollo, usar tiempos de caché más cortos para facilitar testing
    $config['cache_expiration'] = 300; // 5 minutos
    $config['redis']['timeout'] = 1;   // Timeout más corto
    
} elseif (ENVIRONMENT === 'testing') {
    // En testing, usar caché mínimo
    $config['cache_expiration'] = 60;  // 1 minuto
    $config['redis']['database'] = 1;  // Base de datos separada para tests
    
} elseif (ENVIRONMENT === 'production') {
    // En producción, optimizar para performance
    $config['cache_expiration'] = 7200; // 2 horas
    $config['redis']['timeout'] = 10;   // Timeout más largo
    $config['redis']['options']['persistent'] = true; // Conexiones persistentes
}

// ============================================================================
// CONFIGURACIÓN AVANZADA
// ============================================================================

// Configuración para múltiples servidores Redis (cluster/sentinel)
$config['redis_cluster'] = array(
    'enabled' => false,
    'servers' => array(
        array('host' => '127.0.0.1', 'port' => 6379),
        array('host' => '127.0.0.1', 'port' => 6380),
        array('host' => '127.0.0.1', 'port' => 6381),
    ),
    'options' => array(
        'cluster' => 'redis',
    )
);

// Configuración de logging para caché
$config['cache_logging'] = array(
    // Habilitar logging de operaciones de caché
    'enabled' => ENVIRONMENT === 'development',
    
    // Nivel de logging: 'error', 'info', 'debug'
    'level' => 'info',
    
    // Registrar operaciones lentas (en segundos)
    'slow_query_threshold' => 0.1,
);

// ============================================================================
// CONFIGURACIÓN DE FALLBACK
// ============================================================================

// Configuración del driver de respaldo (file)
$config['file_cache'] = array(
    // Directorio para archivos de caché
    'cache_path' => APPPATH . 'cache/',
    
    // Tiempo de vida por defecto para caché de archivos
    'default_ttl' => 3600,
    
    // Limpiar archivos expirados automáticamente
    'auto_cleanup' => true,
);

// ============================================================================
// CONFIGURACIÓN DE MEMCACHED (ALTERNATIVA)
// ============================================================================

// Configuración para Memcached como alternativa a Redis
$config['memcached'] = array(
    'default' => array(
        'hostname' => '127.0.0.1',
        'port' => '11211',
        'weight' => '1',
    ),
);

// ============================================================================
// NOTAS DE CONFIGURACIÓN
// ============================================================================

/*
NOTAS IMPORTANTES:

1. PREDIS vs DRIVER NATIVO:
   - Predis: Más estable, mejor mantenido, sin dependencias de extensiones
   - Driver nativo: Requiere extensión phpredis, puede ser más rápido en algunos casos

2. CONFIGURACIÓN DE PRODUCCIÓN:
   - Usar conexiones persistentes para mejor performance
   - Configurar timeouts apropiados según la latencia de red
   - Monitorear uso de memoria Redis
   - Implementar alertas para fallos de conexión

3. SEGURIDAD:
   - Usar contraseñas fuertes para Redis
   - Configurar Redis para escuchar solo en interfaces necesarias
   - Considerar usar SSL/TLS para conexiones remotas

4. PERFORMANCE:
   - Ajustar tiempos de caché según patrones de uso
   - Usar prefijos para organizar claves
   - Monitorear hit rate del caché
   - Implementar estrategias de invalidación eficientes

5. DEBUGGING:
   - Habilitar logging en desarrollo
   - Usar herramientas como Redis CLI para inspeccionar datos
   - Monitorear métricas de Redis (memoria, conexiones, comandos)

6. BACKUP Y RECUPERACIÓN:
   - Configurar persistencia Redis (RDB/AOF)
   - Implementar backups regulares
   - Probar procedimientos de recuperación

Para más información:
- Documentación Predis: https://github.com/predis/predis
- Documentación Redis: https://redis.io/documentation
- CodeIgniter Cache: https://codeigniter.com/userguide3/libraries/caching.html
*/
