<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Cacheable Trait
 *
 * Este trait proporciona funcionalidad de caché con sistema de versiones
 * para modelos de CodeIgniter.
 *
 * Permite invalidar automáticamente la caché cuando se realizan operaciones
 * de escritura, utilizando un sistema de versiones por tabla.
 */
trait CacheableTrait
{
    /**
     * Tiempo de caché para los datos (en segundos)
     * @var int|null
     */
    protected $cache_time = 300; // 5 minutos por defecto

    /**
     * Tiempo de caché para las claves de versión (en segundos)
     * @var int|null
     */
    protected $version_cache_time = null; // null = nunca expira

    /**
     * Nombre de la tabla para la que se gestiona la caché
     * @var string
     */
    protected $cache_table;

    /**
     * Instancia del driver de caché (Predis o CI nativo)
     * @var CI_Cache|Predis_cache_adapter
     */
    protected $cache;

    /**
     * Inicializa el sistema de caché
     *
     * @param string $table Nombre de la tabla
     * @return void
     */
    protected function init_cache($table)
    {
        $this->cache_table = $table;

        // Obtener la instancia de CI
        $CI = &get_instance();

        // Cargar el driver de caché si no está ya cargado
        if (!isset($this->cache)) {
            if (!isset($CI->cache)) {
                // Verificar si se debe usar Predis o el driver nativo
                $use_predis = $CI->config->item('use_predis_cache');

                if ($use_predis !== FALSE) {
                    // Usar Predis Cache Adapter
                    $cache_config = [
                        'adapter' => $CI->config->item('cache_driver'),  // 'redis'
                        'backup'  => $CI->config->item('cache_backup'),   // 'file'
                        'redis'   => $CI->config->item('redis')          // Array con host, password, port, timeout
                    ];

                    $CI->load->library('Predis_cache_driver');
                    $this->cache = new Predis_cache_adapter($cache_config);
                } else {
                    // Usar driver nativo de CI
                    $CI->load->driver('cache', [
                        'adapter' => $CI->config->item('cache_driver'),  // 'redis'
                        'backup'  => $CI->config->item('cache_backup'),   // 'file'
                        'redis'   => $CI->config->item('redis')          // Array con host, password, port, timeout
                    ]);
                    $this->cache = $CI->cache;
                }
            } else {
                $this->cache = $CI->cache;
            }
        }
    }

    /**
     * Set the cache time for this model
     *
     * @param int|null $seconds Cache duration in seconds (null = never expire)
     * @return $this For method chaining
     */
    public function set_cache_time($seconds)
    {
        $this->cache_time = ($seconds === null) ? null : (int) $seconds;
        return $this;
    }

    /**
     * Set the cache time for version keys
     *
     * @param int|null $seconds Cache duration in seconds (null = never expire)
     * @return $this For method chaining
     */
    public function set_version_cache_time($seconds)
    {
        $this->version_cache_time = ($seconds === null) ? null : (int) $seconds;
        return $this;
    }

    /**
     * Generate cache key
     *
     * @param string $function_name Function name
     * @param array $params Parameters
     * @return string Cache key
     */
    protected function generate_cache_key($function_name, $params = [])
    {
        ksort($params);
        $params_str = json_encode($params);
        $hashed_params = md5($params_str);
        $version = $this->get_cache_version();
        return "{$this->cache_table}:v{$version}:{$function_name}_{$hashed_params}";
    }

    /**
     * Get data with cache
     *
     * @param string $cache_key Cache key
     * @param callable $query_callback Callback function to get data if not in cache
     * @return mixed Cached data or fresh data
     */
    protected function get_cached_data($cache_key, $query_callback)
    {
        $cached_data = $this->cache->get($cache_key);
        if ($cached_data !== false) {
            return $cached_data;
        }
        $data = $query_callback();

        // Guardar en caché
        $this->save_with_proper_ttl($cache_key, $data, $this->cache_time);
        return $data;
    }

    /**
     * Save data to cache with proper TTL handling
     *
     * @param string $key Cache key
     * @param mixed $data Data to save
     * @param int|null $ttl Time to live in seconds (null = never expire)
     * @return bool Success or failure
     */
    protected function save_with_proper_ttl($key, $data, $ttl)
    {
        // Si TTL es null, usamos un valor muy grande (10 años)
        // Redis no permite TTL = 0 para indicar "nunca expira"
        if ($ttl === null) {
            $ttl = 315360000; // 10 años en segundos
        } else {
            // Asegurarnos de que TTL sea al menos 1 segundo
            $ttl = max(1, (int) $ttl);
        }

        return $this->cache->save($key, $data, $ttl);
    }

    /**
     * Clear a specific cache key
     *
     * @param string $cache_key Cache key to clear
     * @return void
     */
    protected function clear_cache($cache_key)
    {
        $this->cache->delete($cache_key);
    }

    /**
     * Get the current cache version for this table
     *
     * @return int Current cache version
     */
    protected function get_cache_version()
    {
        $version_key = "{$this->cache_table}:version";
        $version = $this->cache->get($version_key);
        if ($version === false) {
            // Si no existe, la inicializamos a 1
            $this->save_with_proper_ttl($version_key, 1, $this->version_cache_time);
            return 1;
        }
        return (int) $version;
    }

    /**
     * Increment the cache version for this table
     *
     * @return int New cache version
     */
    protected function bump_cache_version()
    {
        $version_key = "{$this->cache_table}:version";

        // Verificar si la clave existe
        $current_version = $this->cache->get($version_key);

        if ($current_version === false) {
            // Si no existe, inicializamos a 1
            $this->save_with_proper_ttl($version_key, 1, $this->version_cache_time);
            return 1;
        }

        // Usar el método increment() del driver de caché de CI
        // Este método es seguro y está disponible en todos los drivers
        $new_version = $this->cache->increment($version_key, 1);

        // Si por alguna razón increment() falla, usamos el fallback
        if ($new_version === false) {
            $new_version = (int)$current_version + 1;
            $this->save_with_proper_ttl($version_key, $new_version, $this->version_cache_time);
        }

        return $new_version;
    }
}
