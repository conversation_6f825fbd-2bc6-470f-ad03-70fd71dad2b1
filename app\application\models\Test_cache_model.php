<?php
defined('BASEPATH') or exit('No direct script access allowed');

// Cargar el trait
require_once APPPATH . 'traits/CacheableTrait.php';

/**
 * Test Cache Model
 *
 * Modelo de prueba para verificar el funcionamiento del CacheableTrait
 * con la nueva implementación de Predis
 */
class Test_cache_model extends CI_Model
{
    // Usar el trait de caché
    use CacheableTrait;

    private $table = 'test_cache_table';

    public function __construct()
    {
        parent::__construct();

        // Inicializar el sistema de caché con el nombre de la tabla
        $this->init_cache($this->table);

        // Configurar tiempos de caché personalizados para testing
        $this->set_cache_time(300); // 5 minutos para los datos
        $this->set_version_cache_time(null); // Las claves de versión nunca expiran
    }

    /**
     * Obtener datos de prueba (simulados)
     *
     * @return array
     */
    public function get_test_data()
    {
        // Generar clave de caché
        $cache_key = $this->generate_cache_key('get_test_data', []);

        // Obtener datos con caché
        return $this->get_cached_data($cache_key, function() {
            // Simular una consulta costosa
            usleep(100000); // 100ms de delay para simular consulta lenta
            
            return [
                ['id' => 1, 'name' => 'Test Item 1', 'created' => date('Y-m-d H:i:s')],
                ['id' => 2, 'name' => 'Test Item 2', 'created' => date('Y-m-d H:i:s')],
                ['id' => 3, 'name' => 'Test Item 3', 'created' => date('Y-m-d H:i:s')],
            ];
        });
    }

    /**
     * Obtener datos con parámetros
     *
     * @param array $params
     * @return array
     */
    public function get_test_data_with_params($params = [])
    {
        $cache_key = $this->generate_cache_key('get_test_data_with_params', $params);

        return $this->get_cached_data($cache_key, function() use ($params) {
            // Simular consulta con parámetros
            $limit = isset($params['limit']) ? $params['limit'] : 10;
            $filter = isset($params['filter']) ? $params['filter'] : '';
            
            $data = [];
            for ($i = 1; $i <= $limit; $i++) {
                $data[] = [
                    'id' => $i,
                    'name' => "Test Item $i" . ($filter ? " ($filter)" : ''),
                    'created' => date('Y-m-d H:i:s')
                ];
            }
            
            return $data;
        });
    }

    /**
     * Simular una operación de escritura que invalida caché
     *
     * @param array $data
     * @return bool
     */
    public function save_test_data($data)
    {
        // Simular guardado en base de datos
        $success = true; // Simular éxito
        
        if ($success) {
            // Invalidar caché incrementando la versión
            $new_version = $this->bump_cache_version();
            log_message('info', "Test Cache: Versión incrementada a {$new_version}");
        }
        
        return $success;
    }

    /**
     * Método público para acceder a get_cache_version (solo para testing)
     *
     * @return int
     */
    public function get_cache_version_public()
    {
        return $this->get_cache_version();
    }

    /**
     * Método público para acceder a bump_cache_version (solo para testing)
     *
     * @return int
     */
    public function bump_cache_version_public()
    {
        return $this->bump_cache_version();
    }

    /**
     * Test de diferentes tipos de datos en caché
     *
     * @return array
     */
    public function test_data_types()
    {
        $results = [];
        
        // Test string
        $string_key = $this->generate_cache_key('test_string', []);
        $string_data = $this->get_cached_data($string_key, function() {
            return 'Test string value';
        });
        $results['string'] = $string_data;
        
        // Test array
        $array_key = $this->generate_cache_key('test_array', []);
        $array_data = $this->get_cached_data($array_key, function() {
            return ['key1' => 'value1', 'key2' => 'value2', 'nested' => ['a' => 1, 'b' => 2]];
        });
        $results['array'] = $array_data;
        
        // Test object
        $object_key = $this->generate_cache_key('test_object', []);
        $object_data = $this->get_cached_data($object_key, function() {
            return (object)['property1' => 'value1', 'property2' => 123, 'property3' => true];
        });
        $results['object'] = $object_data;
        
        // Test number
        $number_key = $this->generate_cache_key('test_number', []);
        $number_data = $this->get_cached_data($number_key, function() {
            return 42;
        });
        $results['number'] = $number_data;
        
        // Test boolean
        $boolean_key = $this->generate_cache_key('test_boolean', []);
        $boolean_data = $this->get_cached_data($boolean_key, function() {
            return true;
        });
        $results['boolean'] = $boolean_data;
        
        return $results;
    }

    /**
     * Test de rendimiento de caché
     *
     * @param int $iterations
     * @return array
     */
    public function performance_test($iterations = 100)
    {
        $results = [];
        
        // Test de escritura
        $start_time = microtime(true);
        for ($i = 0; $i < $iterations; $i++) {
            $cache_key = $this->generate_cache_key('perf_test', ['iteration' => $i]);
            $this->get_cached_data($cache_key, function() use ($i) {
                return "Performance test data for iteration $i";
            });
        }
        $write_time = microtime(true) - $start_time;
        $results['write_time'] = $write_time;
        
        // Test de lectura (debería ser más rápido)
        $start_time = microtime(true);
        for ($i = 0; $i < $iterations; $i++) {
            $cache_key = $this->generate_cache_key('perf_test', ['iteration' => $i]);
            $this->get_cached_data($cache_key, function() use ($i) {
                return "This should not be called for iteration $i";
            });
        }
        $read_time = microtime(true) - $start_time;
        $results['read_time'] = $read_time;
        
        $results['iterations'] = $iterations;
        $results['improvement'] = $write_time > 0 ? ($write_time - $read_time) / $write_time * 100 : 0;
        
        return $results;
    }

    /**
     * Limpiar datos de prueba del caché
     *
     * @return bool
     */
    public function clear_test_cache()
    {
        // Incrementar versión para invalidar todo el caché de esta tabla
        $new_version = $this->bump_cache_version();
        log_message('info', "Test Cache: Caché limpiado, nueva versión: {$new_version}");
        
        return true;
    }

    /**
     * Obtener estadísticas del caché de prueba
     *
     * @return array
     */
    public function get_cache_stats()
    {
        return [
            'table' => $this->cache_table,
            'current_version' => $this->get_cache_version(),
            'cache_time' => $this->cache_time,
            'version_cache_time' => $this->version_cache_time
        ];
    }
}
