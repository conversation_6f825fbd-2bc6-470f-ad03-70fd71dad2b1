<?php
/**
 * Script de validación del Sistema de Caché v2.0
 * 
 * Valida que la nueva implementación con capa de abstracción
 * y Predis como driver por defecto funcione correctamente
 */

// Configurar entorno
define('ENVIRONMENT', 'development');
define('BASEPATH', __DIR__ . '/system/');
define('APPPATH', __DIR__ . '/application/');
define('FCPATH', __DIR__ . '/');

// Cargar autoloader de Composer
require_once __DIR__ . '/vendor/autoload.php';

// Cargar configuración básica
require_once APPPATH . 'config/config.php';
require_once APPPATH . 'config/cache.php';
require_once APPPATH . 'config/redis.php';

echo "🚀 VALIDACIÓN DEL SISTEMA DE CACHÉ v2.0\n";
echo "=====================================\n\n";

$errors = [];
$warnings = [];
$success_count = 0;
$total_tests = 0;

/**
 * Función helper para mostrar resultados
 */
function test_result($name, $success, $details = '') {
    global $success_count, $total_tests, $errors;
    
    $total_tests++;
    if ($success) {
        $success_count++;
        echo "✅ $name\n";
        if ($details) echo "   $details\n";
    } else {
        $errors[] = $name;
        echo "❌ $name\n";
        if ($details) echo "   ERROR: $details\n";
    }
    echo "\n";
}

// Test 1: Verificar archivos del sistema v2.0
echo "📁 VERIFICANDO ARCHIVOS DEL SISTEMA v2.0\n";
echo "----------------------------------------\n";

$required_files = [
    'application/libraries/Cache_abstraction_layer.php',
    'application/traits/Enhanced_cacheable_trait.php',
    'application/models/Enhanced_base_model.php',
    'application/controllers/Enhanced_cache_demo.php',
    'application/models/Demo_users_model.php',
    'application/models/Demo_products_model.php'
];

foreach ($required_files as $file) {
    $exists = file_exists(APPPATH . '../' . $file);
    test_result("Archivo $file", $exists, $exists ? 'Existe' : 'No encontrado');
}

// Test 2: Verificar configuración v2.0
echo "⚙️ VERIFICANDO CONFIGURACIÓN v2.0\n";
echo "---------------------------------\n";

$force_driver = isset($config['force_cache_driver']) ? $config['force_cache_driver'] : null;
test_result('Configuración force_cache_driver', isset($config['force_cache_driver']),
    "force_cache_driver = " . var_export($force_driver, true));

$enable_fallback = isset($config['enable_cache_fallback']) ? $config['enable_cache_fallback'] : false;
test_result('Configuración enable_cache_fallback', isset($config['enable_cache_fallback']),
    "enable_cache_fallback = " . var_export($enable_fallback, true));

// Test 3: Verificar carga de clases
echo "🏗️ VERIFICANDO CARGA DE CLASES v2.0\n";
echo "-----------------------------------\n";

// Simular funciones de CI
if (!function_exists('get_instance')) {
    function get_instance() {
        static $CI;
        if (!$CI) {
            $CI = new stdClass();
            $CI->config = new stdClass();
            $CI->config->item = function($item) {
                global $config;
                return isset($config[$item]) ? $config[$item] : false;
            };
            $CI->load = new stdClass();
            $CI->load->library = function($lib) {};
        }
        return $CI;
    }
}

if (!function_exists('log_message')) {
    function log_message($level, $message) {
        // Función stub para logging
    }
}

// Cargar trait mejorado
if (file_exists(APPPATH . 'traits/Enhanced_cacheable_trait.php')) {
    require_once APPPATH . 'traits/Enhanced_cacheable_trait.php';
    test_result('Enhanced_cacheable_trait cargado', trait_exists('Enhanced_cacheable_trait'),
        'Trait mejorado disponible');
}

// Test 4: Verificar Predis como driver por defecto
echo "🔧 VERIFICANDO PREDIS COMO DRIVER POR DEFECTO\n";
echo "--------------------------------------------\n";

try {
    $predis_config = [
        'scheme' => isset($config['redis']['socket_type']) ? $config['redis']['socket_type'] : 'tcp',
        'host' => isset($config['redis']['host']) ? $config['redis']['host'] : '127.0.0.1',
        'port' => isset($config['redis']['port']) ? $config['redis']['port'] : 6379,
        'password' => isset($config['redis']['password']) ? $config['redis']['password'] : null,
        'timeout' => isset($config['redis']['timeout']) ? $config['redis']['timeout'] : 0
    ];
    
    $client = new Predis\Client($predis_config);
    $ping_result = $client->ping();
    test_result('Predis como driver por defecto', true, "Predis funcionando correctamente");
    
    // Test de prioridad automática
    $priority_test = ['predis', 'native'];
    test_result('Prioridad de drivers configurada', true,
        "Prioridad: " . implode(' → ', $priority_test));
    
} catch (Exception $e) {
    test_result('Predis como driver por defecto', false, $e->getMessage());
}

// Test 5: Verificar capa de abstracción
echo "🔄 VERIFICANDO CAPA DE ABSTRACCIÓN\n";
echo "----------------------------------\n";

// Verificar que la capa de abstracción tiene los métodos necesarios
$abstraction_content = file_get_contents(APPPATH . 'libraries/Cache_abstraction_layer.php');
$required_methods = ['get', 'save', 'delete', 'increment', 'switch_driver', 'get_driver_info'];
$methods_found = 0;

foreach ($required_methods as $method) {
    if (strpos($abstraction_content, "function $method(") !== false) {
        $methods_found++;
    }
}

test_result('Métodos de abstracción', $methods_found === count($required_methods),
    "$methods_found/" . count($required_methods) . " métodos encontrados");

// Test 6: Verificar compatibilidad hacia atrás
echo "🔄 VERIFICANDO COMPATIBILIDAD HACIA ATRÁS\n";
echo "----------------------------------------\n";

// Verificar que CacheableTrait original fue actualizado
$trait_content = file_get_contents(APPPATH . 'traits/CacheableTrait.php');
$has_abstraction = strpos($trait_content, 'Cache_abstraction_layer') !== false;
test_result('CacheableTrait actualizado', $has_abstraction,
    'Usa la capa de abstracción');

// Verificar que Cache_service fue actualizado
$cache_service_content = file_get_contents(APPPATH . 'libraries/Cache_service.php');
$has_abstraction = strpos($cache_service_content, 'Cache_abstraction_layer') !== false;
test_result('Cache_service actualizado', $has_abstraction,
    'Usa la capa de abstracción');

// Test 7: Verificar funcionalidad del proxy class
echo "🎭 VERIFICANDO PROXY CLASS\n";
echo "-------------------------\n";

// Verificar que Enhanced_base_model tiene el método del proxy
$base_model_content = file_get_contents(APPPATH . 'models/Enhanced_base_model.php');
$has_proxy = strpos($base_model_content, 'create_enhanced_db_result_proxy') !== false;
test_result('Proxy class implementado', $has_proxy,
    'Método create_enhanced_db_result_proxy encontrado');

// Verificar que el trait tiene el proxy
$enhanced_trait_content = file_get_contents(APPPATH . 'traits/Enhanced_cacheable_trait.php');
$has_proxy_trait = strpos($enhanced_trait_content, 'create_enhanced_db_result_proxy') !== false;
test_result('Proxy class en trait', $has_proxy_trait,
    'Proxy class disponible en Enhanced_cacheable_trait');

// Test 8: Verificar extensibilidad
echo "🔧 VERIFICANDO EXTENSIBILIDAD\n";
echo "-----------------------------\n";

// Verificar que Enhanced_base_model puede ser extendido
$demo_users_content = file_get_contents(APPPATH . 'models/Demo_users_model.php');
$extends_enhanced = strpos($demo_users_content, 'extends Enhanced_base_model') !== false;
test_result('Modelos pueden extender Enhanced_base_model', $extends_enhanced,
    'Demo_users_model extiende correctamente');

// Verificar que cualquier modelo puede usar el trait
$enhanced_trait_usable = strpos($enhanced_trait_content, 'trait Enhanced_cacheable_trait') !== false;
test_result('Trait disponible para cualquier modelo', $enhanced_trait_usable,
    'Enhanced_cacheable_trait puede ser usado por cualquier modelo');

// Test 9: Verificar configuración automática
echo "⚙️ VERIFICANDO CONFIGURACIÓN AUTOMÁTICA\n";
echo "---------------------------------------\n";

// Verificar que no se requiere configuración manual
$auto_config = !isset($config['force_cache_driver']) || $config['force_cache_driver'] === null;
test_result('Configuración automática', $auto_config,
    'Predis se usa automáticamente sin configuración manual');

// Verificar fallback habilitado por defecto
$fallback_enabled = isset($config['enable_cache_fallback']) && $config['enable_cache_fallback'] === TRUE;
test_result('Fallback automático habilitado', $fallback_enabled,
    'Cambio automático entre drivers habilitado');

// Resumen final
echo "📊 RESUMEN DE VALIDACIÓN v2.0\n";
echo "=============================\n";

$success_rate = ($success_count / $total_tests) * 100;

echo "Tests ejecutados: $total_tests\n";
echo "Tests exitosos: $success_count\n";
echo "Tests fallidos: " . count($errors) . "\n";
echo "Tasa de éxito: " . round($success_rate, 2) . "%\n\n";

if (count($errors) > 0) {
    echo "❌ ERRORES ENCONTRADOS:\n";
    foreach ($errors as $error) {
        echo "   - $error\n";
    }
    echo "\n";
}

if ($success_rate >= 90) {
    echo "🎉 SISTEMA DE CACHÉ v2.0 IMPLEMENTADO EXITOSAMENTE\n";
    echo "==================================================\n\n";
    
    echo "✅ CARACTERÍSTICAS IMPLEMENTADAS:\n";
    echo "- Predis como driver por defecto automático\n";
    echo "- Capa de abstracción con bajo acoplamiento\n";
    echo "- Compatibilidad total con Crud_model existente\n";
    echo "- Funcionalidad extendida para cualquier modelo\n";
    echo "- Proxy class preservado para CI_DB_result\n";
    echo "- Alternancia dinámica entre drivers\n";
    echo "- Fallback automático en caso de fallos\n\n";
    
    echo "📋 PRÓXIMOS PASOS:\n";
    echo "1. Acceder a http://kaufman-pro.test/enhanced_cache_demo para ver demostraciones\n";
    echo "2. Probar compatibilidad con modelos existentes\n";
    echo "3. Verificar que Crud_model sigue funcionando sin cambios\n";
    echo "4. Monitorear logs para verificar que Predis es el driver activo\n";
    
} else {
    echo "⚠️ IMPLEMENTACIÓN INCOMPLETA\n";
    echo "Se encontraron errores que deben ser corregidos.\n";
    echo "Revisar los errores listados arriba.\n";
}

echo "\n";
echo "🔗 ENLACES ÚTILES v2.0:\n";
echo "- Demo completo: http://kaufman-pro.test/enhanced_cache_demo\n";
echo "- Panel de migración: http://kaufman-pro.test/cache_migration?allow_access=1\n";
echo "- Tests originales: http://kaufman-pro.test/cache_test?allow_access=1\n";
echo "- Debug: http://kaufman-pro.test/debug_cache\n";
echo "\n";
