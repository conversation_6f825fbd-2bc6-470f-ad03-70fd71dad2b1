<?php

class PerformanceHook
{
    protected $ci;
    protected $benchmark_name = 'total_execution';

    public function __construct()
    {
        // No inicializamos $ci aquí, lo haremos en post_system
    }

    public function pre_system()
    {
        // Iniciamos el benchmark al comienzo de la ejecución del sistema
        // Esto es seguro porque no requiere el objeto CI
        $BM =& load_class('Benchmark', 'core');
        $BM->mark($this->benchmark_name . '_start');
    }

    public function post_system()
    {
        // Ahora podemos inicializar $ci de forma segura
        $this->ci =& get_instance();

        // Finalizamos el benchmark
        $this->ci->benchmark->mark($this->benchmark_name . '_end');

        // Obtenemos el tiempo de ejecución
        $execution_time = $this->ci->benchmark->elapsed_time($this->benchmark_name . '_start', $this->benchmark_name . '_end');

        if ($execution_time > 15)
        {
            $this->queue_performance_alert($execution_time);
        }
    }

    protected function queue_performance_alert($execution_time): void
    {
        // Obtenemos información del usuario
        $user_data = $this->get_user_info();
        $alert_data = [
            'type' => 'performance',
            'url' => current_url(),
            'execution_time' => $execution_time,
            'datetime' => date('Y-m-d H:i:s'),
            'memory_usage' => memory_get_peak_usage(true),  // Uso máximo de memoria en bytes
            'db_queries' => isset($this->ci->db) ? count($this->ci->db->queries) : 0,  // Cantidad de consultas a la base de datos
            'db_query_times' => isset($this->ci->db) ? $this->ci->db->query_times : [],  // Tiempos de consulta
            'response_size' => strlen($this->ci->output->get_output()),  // Tamaño de la respuesta
            'loaded_files' => count(get_included_files()),  // //count(get_included_files())  // Cantidad de archivos cargados
            'user_info' => $user_data  // Agregamos la información del usuario
        ];

        // Usar la capa de abstracción de caché v2.0
        if (!isset($this->ci->cache_abstraction_layer)) {
            $this->ci->load->library('Cache_abstraction_layer');
        }

        $current_alerts = $this->ci->cache_abstraction_layer->get('alerts_queue');
        $alerts_array = $current_alerts ? json_decode($current_alerts, true) : [];
        $alerts_array[] = $alert_data;

        $this->ci->cache_abstraction_layer->save('alerts_queue', json_encode($alerts_array), 3600);
    }

    /**
    * Obtiene la información del usuario actual
    * @return array
    */
    protected function get_user_info(): array
    {
        // Verificamos si la sesión está cargada
        if (!isset($this->ci->session)) {
            $this->ci->load->library('session');
        }

        // Información básica del usuario
        $user_info = [
            'user_id' => $this->ci->session->userdata('user_id') ?? null,
            'username' => $this->ci->session->userdata('username') ?? null,
            'email' => $this->ci->session->userdata('email') ?? null,
            'ip_address' => $this->ci->input->ip_address(),
            'user_agent' => $this->ci->input->user_agent()
        ];

        // Filtrar valores nulos
        return array_filter($user_info, function($value) {
            return !is_null($value);
        });
    }
}