<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Cache Abstraction Layer
 *
 * Capa de abstracción que gestiona automáticamente el driver de caché
 * Predis es el driver por defecto, con fallback al driver nativo de CI
 * Proporciona una interfaz unificada y transparente para todos los modelos
 *
 * @package    CodeIgniter
 * @subpackage Libraries
 * @category   Cache
 * <AUTHOR> Pro Team
 * @version    2.0.0
 */
class Cache_abstraction_layer
{
    /**
     * Instancia de CodeIgniter
     * @var CI_Controller
     */
    protected $CI;

    /**
     * Driver de caché activo
     * @var mixed
     */
    protected $active_driver;

    /**
     * Tipo de driver activo
     * @var string
     */
    protected $driver_type;

    /**
     * Configuración de caché
     * @var array
     */
    protected $config;

    /**
     * Estado de inicialización
     * @var bool
     */
    protected $initialized = false;

    /**
     * Drivers disponibles en orden de preferencia
     * @var array
     */
    protected $driver_priority = ['predis', 'native'];

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->CI =& get_instance();
        $this->load_config();
        $this->initialize_driver();
        log_message('info', 'Cache Abstraction Layer Initialized');
    }

    /**
     * Carga la configuración de caché
     */
    protected function load_config()
    {
        // Cargar configuraciones
        $this->CI->config->load('cache', TRUE);
        $this->CI->config->load('redis', TRUE);

        // Configuración por defecto con Predis como preferencia
        $this->config = [
            'driver' => $this->CI->config->item('cache_driver') ?: 'redis',
            'backup' => $this->CI->config->item('cache_backup') ?: 'file',
            'redis' => $this->CI->config->item('redis') ?: [],
            'force_driver' => $this->CI->config->item('force_cache_driver') ?: null,
            'enable_fallback' => $this->CI->config->item('enable_cache_fallback') !== FALSE
        ];
    }

    /**
     * Inicializa el driver de caché apropiado
     */
    protected function initialize_driver()
    {
        // Si hay un driver forzado, usarlo
        if ($this->config['force_driver']) {
            $this->initialize_specific_driver($this->config['force_driver']);
            return;
        }

        // Intentar drivers en orden de prioridad
        foreach ($this->driver_priority as $driver_type) {
            if ($this->initialize_specific_driver($driver_type)) {
                break;
            }
        }

        if (!$this->initialized) {
            log_message('error', 'Cache Abstraction: No cache driver could be initialized');
            throw new Exception('No cache driver available');
        }
    }

    /**
     * Inicializa un driver específico
     *
     * @param string $driver_type
     * @return bool
     */
    protected function initialize_specific_driver($driver_type)
    {
        try {
            switch ($driver_type) {
                case 'predis':
                    return $this->initialize_predis_driver();
                
                case 'native':
                    return $this->initialize_native_driver();
                
                default:
                    log_message('error', "Cache Abstraction: Unknown driver type: $driver_type");
                    return false;
            }
        } catch (Exception $e) {
            log_message('error', "Cache Abstraction: Failed to initialize $driver_type driver - " . $e->getMessage());
            return false;
        }
    }

    /**
     * Inicializa el driver Predis
     *
     * @return bool
     */
    protected function initialize_predis_driver()
    {
        // Verificar disponibilidad de Predis
        if (!class_exists('Predis\\Client')) {
            $vendor_autoload = APPPATH . '../vendor/autoload.php';
            if (file_exists($vendor_autoload)) {
                require_once $vendor_autoload;
            }
            
            if (!class_exists('Predis\\Client')) {
                log_message('info', 'Cache Abstraction: Predis not available, trying next driver');
                return false;
            }
        }

        // Cargar servicio Predis
        $this->CI->load->library('Predis_cache_service');
        
        if ($this->CI->predis_cache_service->is_supported()) {
            $this->active_driver = $this->CI->predis_cache_service;
            $this->driver_type = 'predis';
            $this->initialized = true;
            
            log_message('info', 'Cache Abstraction: Predis driver initialized successfully');
            return true;
        }

        return false;
    }

    /**
     * Inicializa el driver nativo de CI
     *
     * @return bool
     */
    protected function initialize_native_driver()
    {
        try {
            $this->CI->load->driver('cache', [
                'adapter' => $this->config['driver'],
                'backup'  => $this->config['backup'],
                'redis'   => $this->config['redis']
            ]);

            if (isset($this->CI->cache)) {
                $this->active_driver = $this->CI->cache;
                $this->driver_type = 'native';
                $this->initialized = true;
                
                log_message('info', 'Cache Abstraction: Native CI driver initialized successfully');
                return true;
            }
        } catch (Exception $e) {
            log_message('error', 'Cache Abstraction: Native driver initialization failed - ' . $e->getMessage());
        }

        return false;
    }

    /**
     * Obtiene un valor del caché
     *
     * @param string $key
     * @return mixed
     */
    public function get($key)
    {
        if (!$this->initialized) {
            return FALSE;
        }

        try {
            return $this->active_driver->get($key);
        } catch (Exception $e) {
            log_message('error', "Cache Abstraction: Get failed for key '$key' - " . $e->getMessage());
            return $this->handle_fallback('get', [$key]);
        }
    }

    /**
     * Guarda un valor en el caché
     *
     * @param string $key
     * @param mixed $value
     * @param int $ttl
     * @return bool
     */
    public function save($key, $value, $ttl = 60)
    {
        if (!$this->initialized) {
            return FALSE;
        }

        try {
            return $this->active_driver->save($key, $value, $ttl);
        } catch (Exception $e) {
            log_message('error', "Cache Abstraction: Save failed for key '$key' - " . $e->getMessage());
            return $this->handle_fallback('save', [$key, $value, $ttl]);
        }
    }

    /**
     * Elimina una clave del caché
     *
     * @param string $key
     * @return bool
     */
    public function delete($key)
    {
        if (!$this->initialized) {
            return FALSE;
        }

        try {
            return $this->active_driver->delete($key);
        } catch (Exception $e) {
            log_message('error', "Cache Abstraction: Delete failed for key '$key' - " . $e->getMessage());
            return $this->handle_fallback('delete', [$key]);
        }
    }

    /**
     * Incrementa un valor numérico
     *
     * @param string $key
     * @param int $offset
     * @return mixed
     */
    public function increment($key, $offset = 1)
    {
        if (!$this->initialized) {
            return FALSE;
        }

        try {
            return $this->active_driver->increment($key, $offset);
        } catch (Exception $e) {
            log_message('error', "Cache Abstraction: Increment failed for key '$key' - " . $e->getMessage());
            return $this->handle_fallback('increment', [$key, $offset]);
        }
    }

    /**
     * Decrementa un valor numérico
     *
     * @param string $key
     * @param int $offset
     * @return mixed
     */
    public function decrement($key, $offset = 1)
    {
        if (!$this->initialized) {
            return FALSE;
        }

        try {
            return $this->active_driver->decrement($key, $offset);
        } catch (Exception $e) {
            log_message('error', "Cache Abstraction: Decrement failed for key '$key' - " . $e->getMessage());
            return $this->handle_fallback('decrement', [$key, $offset]);
        }
    }

    /**
     * Limpia todo el caché
     *
     * @return bool
     */
    public function clean()
    {
        if (!$this->initialized) {
            return FALSE;
        }

        try {
            return $this->active_driver->clean();
        } catch (Exception $e) {
            log_message('error', "Cache Abstraction: Clean failed - " . $e->getMessage());
            return $this->handle_fallback('clean', []);
        }
    }

    /**
     * Obtiene metadatos de una clave
     *
     * @param string $key
     * @return array|FALSE
     */
    public function get_metadata($key)
    {
        if (!$this->initialized) {
            return FALSE;
        }

        try {
            return $this->active_driver->get_metadata($key);
        } catch (Exception $e) {
            log_message('error', "Cache Abstraction: Get metadata failed for key '$key' - " . $e->getMessage());
            return $this->handle_fallback('get_metadata', [$key]);
        }
    }

    /**
     * Maneja el fallback a otro driver
     *
     * @param string $method
     * @param array $args
     * @return mixed
     */
    protected function handle_fallback($method, $args)
    {
        if (!$this->config['enable_fallback']) {
            return FALSE;
        }

        // Intentar con el siguiente driver disponible
        $current_index = array_search($this->driver_type, $this->driver_priority);
        
        for ($i = $current_index + 1; $i < count($this->driver_priority); $i++) {
            $fallback_type = $this->driver_priority[$i];
            
            if ($this->initialize_specific_driver($fallback_type)) {
                log_message('info', "Cache Abstraction: Switched to fallback driver: $fallback_type");
                
                try {
                    return call_user_func_array([$this->active_driver, $method], $args);
                } catch (Exception $e) {
                    log_message('error', "Cache Abstraction: Fallback driver $fallback_type also failed - " . $e->getMessage());
                    continue;
                }
            }
        }

        return FALSE;
    }

    /**
     * Fuerza el cambio a un driver específico
     *
     * @param string $driver_type
     * @return bool
     */
    public function switch_driver($driver_type)
    {
        $old_driver = $this->driver_type;
        
        if ($this->initialize_specific_driver($driver_type)) {
            log_message('info', "Cache Abstraction: Manually switched from $old_driver to $driver_type");
            return true;
        }

        log_message('error', "Cache Abstraction: Failed to switch to $driver_type, keeping $old_driver");
        return false;
    }

    /**
     * Obtiene información del driver actual
     *
     * @return array
     */
    public function get_driver_info()
    {
        return [
            'type' => $this->driver_type,
            'initialized' => $this->initialized,
            'class' => get_class($this->active_driver),
            'priority' => $this->driver_priority,
            'config' => $this->config
        ];
    }

    /**
     * Obtiene el driver activo (para operaciones avanzadas)
     *
     * @return mixed
     */
    public function get_active_driver()
    {
        return $this->active_driver;
    }

    /**
     * Método mágico para delegar llamadas no definidas
     *
     * @param string $method
     * @param array $args
     * @return mixed
     */
    public function __call($method, $args)
    {
        if ($this->initialized && method_exists($this->active_driver, $method)) {
            try {
                return call_user_func_array([$this->active_driver, $method], $args);
            } catch (Exception $e) {
                log_message('error', "Cache Abstraction: Method '$method' failed - " . $e->getMessage());
                return $this->handle_fallback($method, $args);
            }
        }

        log_message('error', "Cache Abstraction: Method '$method' not found");
        return FALSE;
    }
}
