<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Test extends CI_Controller {

  public function index() {
    $this->load->view('test/index');
  }

  // Comprobar configuraciones de MySQL
  public function verificar_max_allowed_packet() {
    $query_server = $this->db->query("SHOW VARIABLES LIKE 'max_allowed_packet';");
    $resultado_server = $query_server->row();

    $query_client = $this->db->query("SELECT @@session.max_allowed_packet AS client_max_allowed_packet;");
    $resultado_client = $query_client->row();

    $query_tmpdir = $this->db->query("SHOW VARIABLES LIKE 'tmpdir';");
    $resultado_tmpdir = $query_tmpdir->row();

    // Mostrar resultados
    echo "Servidor: " . $resultado_server->Value . "<br>";
    echo "Cliente: " . $resultado_client->client_max_allowed_packet . "<br>";
    echo "Tmpdir: " . $resultado_tmpdir->Value . "<br>";
  }

  // verificar la informacion de php
  public function verificar_info_php() {
    phpinfo();
  }

  // Mostrar estadísticas del sistema de caché utilizando Redis
  public function mostrar_estadisticas_cache() {
    // Usar Redis en lugar de Memcached
    $redis = new Redis();
    try {
      $redis->connect('127.0.0.1', 6379,);
      // agregar autentificacion
      $redis->auth('tHjGQeXrgmI45Y');
    } catch (RedisException $e) {
      echo "No se pudo conectar al servidor Redis. Error: " . $e->getMessage();
      return;
    }

    // Obtener estadísticas de Redis
    $stats = $redis->info();

    if ($stats) {
      // Mostrar estadísticas clave
      $uptime = isset($stats['uptime_in_seconds']) ? $stats['uptime_in_seconds'] : 0;
      $used_memory = isset($stats['used_memory']) ? $stats['used_memory'] : 0;
      $total_memory = isset($stats['maxmemory']) ? $stats['maxmemory'] : 0; // Si maxmemory no está configurado, Redis usará toda la memoria disponible

      // Hits y misses
      $hits = isset($stats['keyspace_hits']) ? $stats['keyspace_hits'] : 0;
      $misses = isset($stats['keyspace_misses']) ? $stats['keyspace_misses'] : 0;

      // Memoria usada como porcentaje
      $memory_percentage = $total_memory > 0 ? ($used_memory / $total_memory) * 100 : 0;

      // Mostrar las estadísticas de Redis
      echo "<h2>Estadísticas del sistema de caché (Redis)</h2>";
      echo "<p><strong>Consultas rápidas (hits):</strong> $hits</p>";
      echo "<p><strong>Consultas lentas (misses):</strong> $misses</p>";
      echo "<p><strong>Tiempo en funcionamiento:</strong> " . gmdate("H:i:s", $uptime) . " horas</p>";
      echo "<p><strong>Memoria utilizada:</strong> " . round($used_memory / (1024 * 1024), 2) . " MB de " . ($total_memory > 0 ? round($total_memory / (1024 * 1024), 2) . " MB" : "ilimitada") . "</p>";
      echo "<p><strong>Porcentaje de memoria utilizada:</strong> " . round($memory_percentage, 2) . "%</p>";

      // Representación visual de la memoria utilizada
      echo "<div style='width:300px; background-color:#ccc;'>
              <div style='width:" . round($memory_percentage, 2) . "%; background-color:green; height:20px;'></div>
            </div>";

    } else {
      echo "No se pudieron obtener las estadísticas del servidor Redis.";
    }
  }

  // Prueba básica de caché usando Redis
  public function prueba_cache() {
    // Cambiar el driver de cache a Redis
      // Cargar el driver de cache con la configuración
      $this->load->driver('cache', [
          'adapter' => $this->config->item('cache_driver'),  // 'redis'
          'backup'  => $this->config->item('cache_backup'),   // 'file'
          'redis'   => $this->config->item('redis')          // Array con host, password, port, timeout
      ]);

    $key = 'test_key';
    $data = 'Hola Caché en Redis';

    // Guardar en caché
    if ($this->cache->save($key, $data, 300)) { // 300 segundos = 5 minutos
      echo "Datos guardados en caché (Redis).<br>";
    } else {
      echo "Fallo al guardar en caché (Redis).<br>";
    }

    // Obtener de caché
    $cached_data = $this->cache->get($key);
    if ($cached_data) {
      echo "Datos en caché: " . $cached_data;
    } else {
      echo "No se encontraron datos en caché.";
    }
  }

  public function flush_all_cache(): void {
    // Cargar el driver de cache con la configuración
    $this->load->driver('cache', [
        'adapter' => $this->config->item('cache_driver'),  // 'redis'
        'backup'  => $this->config->item('cache_backup'),   // 'file'
        'redis'   => $this->config->item('redis')          // Array con host, password, port, timeout
    ]);

    // Limpiar toda la caché usando el método clean() del driver
    if ($this->cache->clean()) {
        echo "Redis cache flushed successfully.\n";
    } else {
        echo "Failed to flush Redis cache.\n";
    }
  }

  /**
   * Prueba el flujo completo de subida y acceso a archivos en R2
   *
   * Este método realiza las siguientes operaciones:
   * 1. Crea un archivo temporal de prueba
   * 2. Sube el archivo a R2 usando StorageFactory
   * 3. Obtiene la URL pública del archivo
   * 4. Verifica si el archivo existe en R2
   * 5. Muestra enlaces para descargar y eliminar el archivo
   *
   * @return void
   */
  public function test_r2_storage(): void {
    // Cargar las librerías necesarias
    $this->load->library('StorageFactory');

    // Obtener la instancia de almacenamiento
    try {
      $storage = $this->storagefactory->get_instance();
      echo "<h2>Prueba de R2 Storage</h2>";
      echo "<p>Tipo de almacenamiento: " . get_class($storage) . "</p>";
    } catch (Exception $e) {
      echo "<div style='color: red; font-weight: bold;'>Error al obtener instancia de almacenamiento: " . $e->getMessage() . "</div>";
      return;
    }

    // Crear un archivo temporal de prueba
    $temp_dir = get_setting("temp_file_path");
    $temp_file_name = "test_file_" . date("YmdHis") . ".txt";
    $temp_file_path = $temp_dir . $temp_file_name;

    // Escribir contenido en el archivo temporal
    $content = "Este es un archivo de prueba para R2 Storage.\n";
    $content .= "Fecha y hora: " . date("Y-m-d H:i:s") . "\n";
    $content .= "ID de prueba: " . uniqid() . "\n";

    if (file_put_contents($temp_file_path, $content) === false) {
      echo "<div style='color: red; font-weight: bold;'>Error al crear archivo temporal</div>";
      return;
    }

    echo "<p>Archivo temporal creado: $temp_file_path</p>";
    echo "<p>Contenido del archivo:</p>";
    echo "<pre>" . htmlspecialchars($content) . "</pre>";

    // Definir directorio de destino y nombre de archivo
    $destination_dir = "files/r2_test";
    $original_file_name = "test_file_" . date("YmdHis") . ".txt";

    // Subir el archivo a R2
    echo "<h3>Subiendo archivo a R2...</h3>";
    $result = $storage->save($temp_file_path, $destination_dir, $original_file_name);

    if ($result === false) {
      echo "<div style='color: red; font-weight: bold;'>Error al subir archivo a R2</div>";
      // Eliminar archivo temporal
      @unlink($temp_file_path);
      return;
    }

    echo "<p style='color: green;'>Archivo subido correctamente</p>";
    echo "<p>Resultado de save(): " . htmlspecialchars($result) . "</p>";

    // Construir la ruta completa del archivo en R2
    $file_key = $destination_dir . "/" . $result;
    echo "<p>File Key en R2: " . htmlspecialchars($file_key) . "</p>";

    // Obtener la URL pública del archivo
    try {
      $file_url = $storage->getFileUrl($file_key);
      echo "<p>URL pública del archivo: <a href='" . htmlspecialchars($file_url) . "' target='_blank'>" . htmlspecialchars($file_url) . "</a></p>";
    } catch (Exception $e) {
      echo "<div style='color: red; font-weight: bold;'>Error al obtener URL pública: " . $e->getMessage() . "</div>";
    }

    // Verificar si el archivo existe en R2
    try {
      $exists = $storage->fileExists($file_key);
      echo "<p>¿El archivo existe en R2? " . ($exists ? "Sí" : "No") . "</p>";
    } catch (Exception $e) {
      echo "<div style='color: red; font-weight: bold;'>Error al verificar existencia del archivo: " . $e->getMessage() . "</div>";
    }

    // Mostrar enlaces para descargar y eliminar el archivo
    echo "<h3>Acciones disponibles:</h3>";
    echo "<ul>";
    echo "<li><a href='" . site_url("test/download_r2_file?file_key=" . urlencode($file_key)) . "' target='_blank'>Descargar archivo</a></li>";
    echo "<li><a href='" . site_url("test/delete_r2_file?file_key=" . urlencode($file_key)) . "' onclick='return confirm(\"¿Estás seguro de eliminar este archivo?\")'>Eliminar archivo</a></li>";
    echo "</ul>";

    // Eliminar archivo temporal
    @unlink($temp_file_path);
    echo "<p>Archivo temporal eliminado</p>";

    // Guardar información en sesión para las otras funciones
    $this->session->set_userdata('r2_test_file_key', $file_key);
  }

  /**
   * Descarga un archivo de R2
   *
   * @return void
   */
  public function download_r2_file(): void {
    // Obtener file_key de la URL o de la sesión
    $file_key = $this->input->get('file_key');
    if (empty($file_key)) {
      $file_key = $this->session->userdata('r2_test_file_key');
      if (empty($file_key)) {
        echo "No se especificó un archivo para descargar";
        return;
      }
    }

    // Cargar las librerías necesarias
    $this->load->library('StorageFactory');

    // Obtener la instancia de almacenamiento
    try {
      $storage = $this->storagefactory->get_instance();

      // Verificar si el archivo existe
      if (!$storage->fileExists($file_key)) {
        echo "El archivo no existe en R2";
        return;
      }

      // Descargar el archivo
      $storage->download($file_key);

    } catch (Exception $e) {
      echo "Error al descargar archivo: " . $e->getMessage();
    }
  }

  /**
   * Elimina un archivo de R2
   *
   * @return void
   */
  public function delete_r2_file(): void {
    // Obtener file_key de la URL o de la sesión
    $file_key = $this->input->get('file_key');
    if (empty($file_key)) {
      $file_key = $this->session->userdata('r2_test_file_key');
      if (empty($file_key)) {
        echo "No se especificó un archivo para eliminar";
        return;
      }
    }

    // Cargar las librerías necesarias
    $this->load->library('StorageFactory');

    // Obtener la instancia de almacenamiento
    try {
      $storage = $this->storagefactory->get_instance();

      // Verificar si el archivo existe
      if (!$storage->fileExists($file_key)) {
        echo "<div style='color: red; font-weight: bold;'>El archivo no existe en R2</div>";
        echo "<p><a href='" . site_url("test/test_r2_storage") . "'>Volver a la prueba</a></p>";
        return;
      }

      // Eliminar el archivo
      $result = $storage->delete($file_key);

      if ($result) {
        echo "<div style='color: green; font-weight: bold;'>Archivo eliminado correctamente</div>";
      } else {
        echo "<div style='color: red; font-weight: bold;'>Error al eliminar archivo</div>";
      }

      echo "<p><a href='" . site_url("test/test_r2_storage") . "'>Volver a la prueba</a></p>";

    } catch (Exception $e) {
      echo "<div style='color: red; font-weight: bold;'>Error: " . $e->getMessage() . "</div>";
      echo "<p><a href='" . site_url("test/test_r2_storage") . "'>Volver a la prueba</a></p>";
    }
  }

  /**
   * Prueba específicamente la funcionalidad de getFileUrl() con diferentes tipos de entradas
   *
   * @return void
   */
  public function test_get_file_url(): void {
    // Cargar las librerías necesarias
    $this->load->library('StorageFactory');

    // Obtener la instancia de almacenamiento
    try {
      $storage = $this->storagefactory->get_instance();
      echo "<h2>Prueba de getFileUrl()</h2>";
      echo "<p>Tipo de almacenamiento: " . get_class($storage) . "</p>";
    } catch (Exception $e) {
      echo "<div style='color: red; font-weight: bold;'>Error al obtener instancia de almacenamiento: " . $e->getMessage() . "</div>";
      return;
    }

    // Obtener el file_key de la sesión si existe
    $file_key = $this->session->userdata('r2_test_file_key');

    // Casos de prueba
    $test_cases = [
      [
        'name' => 'Archivo de prueba de sesión',
        'key' => $file_key,
        'description' => 'Archivo subido previamente (guardado en sesión)'
      ],
      [
        'name' => 'Ruta completa con directorio',
        'key' => 'files/r2_test/test_file.txt',
        'description' => 'Ruta completa con directorio y nombre de archivo'
      ],
      [
        'name' => 'Solo nombre de archivo',
        'key' => 'test_file.txt',
        'description' => 'Solo nombre de archivo sin directorio'
      ],
      [
        'name' => 'Nombre de archivo con prefijo',
        'key' => 'file6500af40acf96-test.pdf',
        'description' => 'Nombre de archivo con prefijo generado por el sistema'
      ],
      [
        'name' => 'Ruta con múltiples niveles',
        'key' => 'files/mimasoft_files/client_1/project_1/form_3/elemento_11603/file6500af40acf96-test.pdf',
        'description' => 'Ruta completa con múltiples niveles de directorios'
      ]
    ];

    echo "<style>
      table { border-collapse: collapse; width: 100%; margin-top: 20px; }
      th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
      th { background-color: #f2f2f2; }
      tr:nth-child(even) { background-color: #f9f9f9; }
      .success { color: green; }
      .error { color: red; }
    </style>";

    echo "<table>";
    echo "<tr><th>Caso de prueba</th><th>Entrada</th><th>Descripción</th><th>Resultado</th></tr>";

    foreach ($test_cases as $case) {
      echo "<tr>";
      echo "<td>" . htmlspecialchars($case['name']) . "</td>";
      echo "<td><code>" . htmlspecialchars($case['key']) . "</code></td>";
      echo "<td>" . htmlspecialchars($case['description']) . "</td>";

      try {
        if (empty($case['key'])) {
          echo "<td class='error'>Clave vacía - No se puede probar</td>";
        } else {
          $url = $storage->getFileUrl($case['key']);
          echo "<td class='success'>";
          echo "URL: <a href='" . htmlspecialchars($url) . "' target='_blank'>" . htmlspecialchars($url) . "</a>";
          echo "</td>";
        }
      } catch (Exception $e) {
        echo "<td class='error'>Error: " . htmlspecialchars($e->getMessage()) . "</td>";
      }

      echo "</tr>";
    }

    echo "</table>";

    // Formulario para probar con una entrada personalizada
    echo "<h3>Probar con una entrada personalizada</h3>";
    echo "<form method='post' action='" . site_url("test/test_get_file_url_custom") . "'>";
    echo "<div style='margin-bottom: 10px;'>";
    echo "<label for='custom_key'>Ruta o nombre de archivo:</label><br>";
    echo "<input type='text' id='custom_key' name='custom_key' style='width: 100%; padding: 8px; margin-top: 5px;' placeholder='Ejemplo: files/r2_test/mi_archivo.txt'>";
    echo "</div>";
    echo "<button type='submit' style='padding: 8px 15px; background-color: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;'>Probar</button>";
    echo "</form>";

    echo "<p><a href='" . site_url("test") . "' style='display: inline-block; margin-top: 20px; padding: 8px 15px; background-color: #f0f8ff; border-radius: 4px; border: 1px solid #cce5ff; text-decoration: none;'>Volver a la página de pruebas</a></p>";
  }

  /**
   * Procesa la entrada personalizada para probar getFileUrl()
   *
   * @return void
   */
  public function test_get_file_url_custom(): void {
    $custom_key = $this->input->post('custom_key');

    if (empty($custom_key)) {
      echo "<div style='color: red; font-weight: bold;'>Error: No se proporcionó una ruta o nombre de archivo</div>";
      echo "<p><a href='" . site_url("test/test_get_file_url") . "'>Volver a la prueba</a></p>";
      return;
    }

    // Cargar las librerías necesarias
    $this->load->library('StorageFactory');

    // Obtener la instancia de almacenamiento
    try {
      $storage = $this->storagefactory->get_instance();
      echo "<h2>Resultado de la prueba personalizada</h2>";
      echo "<p>Entrada: <code>" . htmlspecialchars($custom_key) . "</code></p>";

      $url = $storage->getFileUrl($custom_key);
      echo "<p style='color: green;'>URL generada: <a href='" . htmlspecialchars($url) . "' target='_blank'>" . htmlspecialchars($url) . "</a></p>";

      // Intentar verificar si el archivo existe
      try {
        $exists = $storage->fileExists($custom_key);
        echo "<p>¿El archivo existe en R2? <strong>" . ($exists ? "Sí" : "No") . "</strong></p>";
      } catch (Exception $e) {
        echo "<p style='color: orange;'>No se pudo verificar la existencia del archivo: " . $e->getMessage() . "</p>";
      }

    } catch (Exception $e) {
      echo "<div style='color: red; font-weight: bold;'>Error: " . $e->getMessage() . "</div>";
    }

    echo "<p><a href='" . site_url("test/test_get_file_url") . "'>Volver a la prueba</a></p>";
  }

}
