<?php
/**
 * Script para verificar y corregir errores de caché
 * 
 * Este script verifica que todas las correcciones se hayan aplicado
 * correctamente y que el sistema esté funcionando sin errores.
 */

// Configurar entorno
define('ENVIRONMENT', 'development');
define('BASEPATH', __DIR__ . '/system/');
define('APPPATH', __DIR__ . '/application/');
define('FCPATH', __DIR__ . '/');

// Cargar autoloader de Composer
require_once __DIR__ . '/vendor/autoload.php';

echo "🔧 VERIFICACIÓN Y CORRECCIÓN DE ERRORES DE CACHÉ\n";
echo "===============================================\n\n";

$errors_found = 0;
$fixes_applied = 0;

/**
 * Función helper para mostrar resultados
 */
function check_result($name, $success, $details = '') {
    global $errors_found;
    
    if ($success) {
        echo "✅ $name\n";
        if ($details) echo "   $details\n";
    } else {
        $errors_found++;
        echo "❌ $name\n";
        if ($details) echo "   ERROR: $details\n";
    }
    echo "\n";
}

// Test 1: Verificar que Enhanced_base_model esté en autoload
echo "📋 VERIFICANDO AUTOLOAD\n";
echo "----------------------\n";

$autoload_content = file_get_contents(APPPATH . 'config/autoload.php');
$has_enhanced_model = strpos($autoload_content, "'Enhanced_base_model'") !== false;
check_result('Enhanced_base_model en autoload', $has_enhanced_model,
    $has_enhanced_model ? 'Configurado correctamente' : 'Falta agregar al autoload');

$has_cache_helper = strpos($autoload_content, "'cache_compatibility'") !== false;
check_result('Cache compatibility helper en autoload', $has_cache_helper,
    $has_cache_helper ? 'Configurado correctamente' : 'Falta agregar al autoload');

// Test 2: Verificar hooks
echo "🔗 VERIFICANDO HOOKS\n";
echo "-------------------\n";

$hooks_content = file_get_contents(APPPATH . 'config/hooks.php');
$has_cache_hook = strpos($hooks_content, 'Cache_compatibility_hook') !== false;
check_result('Cache compatibility hook configurado', $has_cache_hook,
    $has_cache_hook ? 'Hook configurado' : 'Falta configurar hook');

// Test 3: Verificar archivos de compatibilidad
echo "📁 VERIFICANDO ARCHIVOS DE COMPATIBILIDAD\n";
echo "-----------------------------------------\n";

$compatibility_files = [
    'application/helpers/cache_compatibility_helper.php',
    'application/hooks/Cache_compatibility_hook.php'
];

foreach ($compatibility_files as $file) {
    $exists = file_exists(APPPATH . '../' . $file);
    check_result("Archivo $file", $exists, $exists ? 'Existe' : 'No encontrado');
}

// Test 4: Verificar correcciones en archivos problemáticos
echo "🔧 VERIFICANDO CORRECCIONES APLICADAS\n";
echo "------------------------------------\n";

// Verificar PerformanceHook
$perf_hook_content = file_get_contents(APPPATH . 'hooks/PerformanceHook.php');
$perf_hook_fixed = strpos($perf_hook_content, 'cache_abstraction_layer') !== false;
check_result('PerformanceHook corregido', $perf_hook_fixed,
    $perf_hook_fixed ? 'Usa capa de abstracción' : 'Aún usa sintaxis antigua');

// Verificar AlertProcessor_library
$alert_proc_content = file_get_contents(APPPATH . 'libraries/AlertProcessor_library.php');
$alert_proc_fixed = strpos($alert_proc_content, 'cache_abstraction_layer') !== false;
check_result('AlertProcessor_library corregido', $alert_proc_fixed,
    $alert_proc_fixed ? 'Usa capa de abstracción' : 'Aún usa sintaxis antigua');

// Verificar Test_Alertas
$test_alertas_content = file_get_contents(APPPATH . 'controllers/Test_Alertas.php');
$test_alertas_fixed = strpos($test_alertas_content, 'cache_abstraction_layer') !== false;
check_result('Test_Alertas corregido', $test_alertas_fixed,
    $test_alertas_fixed ? 'Usa capa de abstracción' : 'Aún usa sintaxis antigua');

// Verificar Dashboard typo
$dashboard_content = file_get_contents(APPPATH . 'controllers/Dashboard.php');
$dashboard_fixed = strpos($dashboard_content, '->render(') !== false && strpos($dashboard_content, '->rander(') === false;
check_result('Dashboard typo corregido', $dashboard_fixed,
    $dashboard_fixed ? 'Typo corregido' : 'Aún tiene typo en render()');

// Test 5: Verificar que Predis esté disponible
echo "🔌 VERIFICANDO PREDIS\n";
echo "--------------------\n";

try {
    $predis_available = class_exists('Predis\\Client');
    check_result('Predis disponible', $predis_available,
        $predis_available ? 'Predis\\Client cargado' : 'Predis no disponible');
    
    if ($predis_available) {
        // Test de conexión básica
        $config = [
            'scheme' => 'tcp',
            'host' => '127.0.0.1',
            'port' => 6379,
            'password' => 'tHjGQeXrgmI45Y',
            'timeout' => 5
        ];
        
        $client = new Predis\Client($config);
        $ping_result = $client->ping();
        check_result('Conexión Predis', true, "Ping: $ping_result");
    }
    
} catch (Exception $e) {
    check_result('Conexión Predis', false, $e->getMessage());
}

// Test 6: Buscar otros archivos que puedan tener problemas
echo "🔍 BUSCANDO OTROS PROBLEMAS POTENCIALES\n";
echo "--------------------------------------\n";

$problematic_patterns = [
    '$this->cache->redis->',
    '$CI->cache->redis->',
    '->rander(',
    'Enhanced_base_model' // Sin quotes, podría indicar uso sin autoload
];

$files_to_check = [
    'controllers',
    'libraries', 
    'models',
    'hooks'
];

$potential_issues = [];

foreach ($files_to_check as $dir) {
    $full_dir = APPPATH . $dir;
    if (is_dir($full_dir)) {
        $files = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($full_dir));
        
        foreach ($files as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                $content = file_get_contents($file->getPathname());
                
                foreach ($problematic_patterns as $pattern) {
                    if (strpos($content, $pattern) !== false) {
                        $relative_path = str_replace(APPPATH, 'application/', $file->getPathname());
                        $potential_issues[] = [
                            'file' => $relative_path,
                            'pattern' => $pattern,
                            'line' => 'Unknown'
                        ];
                    }
                }
            }
        }
    }
}

if (empty($potential_issues)) {
    check_result('Búsqueda de problemas adicionales', true, 'No se encontraron problemas adicionales');
} else {
    check_result('Búsqueda de problemas adicionales', false, count($potential_issues) . ' problemas potenciales encontrados');
    
    echo "   PROBLEMAS ENCONTRADOS:\n";
    foreach ($potential_issues as $issue) {
        echo "   - {$issue['file']}: {$issue['pattern']}\n";
    }
}

// Resumen final
echo "📊 RESUMEN DE VERIFICACIÓN\n";
echo "=========================\n";

if ($errors_found === 0) {
    echo "🎉 TODOS LOS ERRORES HAN SIDO CORREGIDOS\n";
    echo "========================================\n\n";
    
    echo "✅ CORRECCIONES APLICADAS:\n";
    echo "- Enhanced_base_model agregado al autoload\n";
    echo "- Helper de compatibilidad creado y cargado\n";
    echo "- Hook de compatibilidad configurado\n";
    echo "- PerformanceHook actualizado para usar capa de abstracción\n";
    echo "- AlertProcessor_library actualizado\n";
    echo "- Test_Alertas actualizado\n";
    echo "- Typo en Dashboard.php corregido\n";
    echo "- Sistema de compatibilidad automática implementado\n\n";
    
    echo "🚀 EL SISTEMA DEBERÍA FUNCIONAR CORRECTAMENTE AHORA\n\n";
    
    echo "📋 PRÓXIMOS PASOS:\n";
    echo "1. Reiniciar el servidor web para cargar los nuevos hooks\n";
    echo "2. Probar las URLs que daban error 500\n";
    echo "3. Verificar logs para confirmar que no hay más errores\n";
    echo "4. Probar funcionalidad de caché en http://kaufman-pro.test/enhanced_cache_demo\n";
    
} else {
    echo "⚠️ SE ENCONTRARON $errors_found ERRORES\n";
    echo "======================================\n";
    echo "Revisar los errores listados arriba y aplicar las correcciones necesarias.\n";
}

echo "\n";
echo "🔗 ENLACES PARA PROBAR:\n";
echo "- Demo de caché v2.0: http://kaufman-pro.test/enhanced_cache_demo\n";
echo "- Panel de migración: http://kaufman-pro.test/cache_migration?allow_access=1\n";
echo "- Dashboard: http://kaufman-pro.test/dashboard\n";
echo "- Test de alertas: http://kaufman-pro.test/test_alertas\n";
echo "\n";
