<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Cache Compatibility Hook
 *
 * Hook que se ejecuta temprano para configurar automáticamente
 * la compatibilidad con código que usa la sintaxis antigua de Redis
 *
 * @package    CodeIgniter
 * @subpackage Hooks
 * @category   Cache
 * <AUTHOR> Pro Team
 * @version    2.0.0
 */
class Cache_compatibility_hook
{
    protected $CI;

    public function __construct()
    {
        $this->CI =& get_instance();
    }

    /**
     * Inicializa la compatibilidad de caché
     * Se ejecuta después de que el controlador se ha instanciado
     */
    public function initialize_cache_compatibility()
    {
        // Solo ejecutar si no está ya configurado
        if (!isset($this->CI->cache) || !is_object($this->CI->cache)) {
            
            // Cargar la capa de abstracción
            if (!isset($this->CI->cache_abstraction_layer)) {
                $this->CI->load->library('Cache_abstraction_layer');
            }
            
            // Crear el wrapper de compatibilidad
            $this->CI->cache = get_cache_instance();
            
            log_message('info', 'Cache Compatibility Hook: Initialized cache compatibility wrapper');
        }
    }

    /**
     * Verifica y corrige referencias a cache->redis en tiempo de ejecución
     */
    public function check_cache_usage()
    {
        // Este método se puede usar para logging o debugging
        if (isset($this->CI->cache) && is_object($this->CI->cache)) {
            $driver_info = $this->CI->cache_abstraction_layer->get_driver_info();
            
            // Log del driver activo (solo en desarrollo)
            if (ENVIRONMENT === 'development') {
                log_message('debug', 'Cache Compatibility: Active driver is ' . $driver_info['type']);
            }
        }
    }
}
