<?php
defined('BASEPATH') or exit('No direct script access allowed');

// Cargar el trait mejorado
require_once APPPATH . 'traits/Enhanced_cacheable_trait.php';

/**
 * Enhanced Base Model
 *
 * Modelo base mejorado que proporciona funcionalidad de caché avanzada
 * Cualquier modelo puede extender de este para obtener capacidades de caché
 * Usa Predis como driver por defecto con fallback automático
 *
 * @package    CodeIgniter
 * @subpackage Models
 * @category   Base
 * <AUTHOR> Pro Team
 * @version    2.0.0
 */
class Enhanced_base_model extends CI_Model
{
    // Usar el trait de caché mejorado
    use Enhanced_cacheable_trait;

    /**
     * Nombre de la tabla principal
     * @var string
     */
    protected $table;

    /**
     * Clave primaria de la tabla
     * @var string
     */
    protected $primary_key = 'id';

    /**
     * Campos que se excluyen automáticamente de las consultas
     * @var array
     */
    protected $excluded_fields = ['deleted'];

    /**
     * Constructor
     *
     * @param string|null $table Nombre de la tabla
     * @param array $cache_options Opciones de caché
     */
    public function __construct($table = null, $cache_options = [])
    {
        parent::__construct();
        
        if ($table) {
            $this->use_table($table, $cache_options);
        }
    }

    /**
     * Configura la tabla y el sistema de caché
     *
     * @param string $table Nombre de la tabla
     * @param array $cache_options Opciones de caché
     * @return $this
     */
    protected function use_table($table, $cache_options = [])
    {
        $this->table = $table;
        
        // Inicializar el sistema de caché mejorado
        $this->init_enhanced_cache($table, $cache_options);
        
        return $this;
    }

    /**
     * Obtiene un registro por ID con caché
     *
     * @param int $id
     * @param bool $include_deleted
     * @return object|null
     */
    public function get_by_id($id, $include_deleted = false)
    {
        $where = [$this->primary_key => $id];
        if (!$include_deleted) {
            $where['deleted'] = 0;
        }

        $cache_key = $this->generate_enhanced_cache_key('get_by_id', $where);
        
        return $this->get_enhanced_cached_data($cache_key, function() use ($where) {
            $result = $this->db->get_where($this->table, $where, 1);
            return $result->num_rows() ? $result->row() : null;
        });
    }

    /**
     * Obtiene todos los registros con caché
     *
     * @param bool $include_deleted
     * @param int $limit
     * @param int $offset
     * @return object Proxy object that behaves like CI_DB_result
     */
    public function get_all($include_deleted = false, $limit = null, $offset = 0)
    {
        $where = $include_deleted ? [] : ['deleted' => 0];
        $params = array_merge($where, ['limit' => $limit, 'offset' => $offset]);
        
        $cache_key = $this->generate_enhanced_cache_key('get_all', $params);

        $cached_data = $this->get_enhanced_cached_data($cache_key, function() use ($where, $limit, $offset) {
            $this->db->where($where);
            if ($limit) {
                $this->db->limit($limit, $offset);
            }
            $query = $this->db->get($this->table);
            return $query->result();
        });

        return $this->create_enhanced_db_result_proxy($cached_data);
    }

    /**
     * Obtiene registros con condiciones específicas
     *
     * @param array $where
     * @param int $limit
     * @param int $offset
     * @param string $order_by
     * @return object Proxy object that behaves like CI_DB_result
     */
    public function get_where($where = [], $limit = null, $offset = 0, $order_by = null)
    {
        // Añadir exclusión de eliminados por defecto
        if (!isset($where['deleted'])) {
            $where['deleted'] = 0;
        }

        $params = array_merge($where, [
            'limit' => $limit, 
            'offset' => $offset, 
            'order_by' => $order_by
        ]);
        
        $cache_key = $this->generate_enhanced_cache_key('get_where', $params);

        $cached_data = $this->get_enhanced_cached_data($cache_key, function() use ($where, $limit, $offset, $order_by) {
            $this->db->where($where);
            
            if ($order_by) {
                $this->db->order_by($order_by);
            }
            
            if ($limit) {
                $this->db->limit($limit, $offset);
            }
            
            $query = $this->db->get($this->table);
            return $query->result();
        });

        return $this->create_enhanced_db_result_proxy($cached_data);
    }

    /**
     * Cuenta registros con condiciones específicas
     *
     * @param array $where
     * @return int
     */
    public function count_where($where = [])
    {
        // Añadir exclusión de eliminados por defecto
        if (!isset($where['deleted'])) {
            $where['deleted'] = 0;
        }

        $cache_key = $this->generate_enhanced_cache_key('count_where', $where);

        return $this->get_enhanced_cached_data($cache_key, function() use ($where) {
            return $this->db->where($where)->count_all_results($this->table);
        });
    }

    /**
     * Inserta un nuevo registro e invalida caché
     *
     * @param array $data
     * @return int|bool Insert ID o FALSE en caso de error
     */
    public function insert($data)
    {
        if ($this->db->insert($this->table, $data)) {
            $insert_id = $this->db->insert_id();
            
            // Invalidar caché
            $this->bump_enhanced_cache_version();
            
            return $insert_id;
        }
        
        return FALSE;
    }

    /**
     * Actualiza registros e invalida caché
     *
     * @param array $data
     * @param array $where
     * @return bool
     */
    public function update($data, $where)
    {
        if ($this->db->update($this->table, $data, $where)) {
            // Invalidar caché
            $this->bump_enhanced_cache_version();
            
            return TRUE;
        }
        
        return FALSE;
    }

    /**
     * Actualiza un registro por ID e invalida caché
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function update_by_id($id, $data)
    {
        return $this->update($data, [$this->primary_key => $id]);
    }

    /**
     * Elimina registros (soft delete) e invalida caché
     *
     * @param array $where
     * @return bool
     */
    public function delete($where)
    {
        return $this->update(['deleted' => 1], $where);
    }

    /**
     * Elimina un registro por ID (soft delete) e invalida caché
     *
     * @param int $id
     * @return bool
     */
    public function delete_by_id($id)
    {
        return $this->delete([$this->primary_key => $id]);
    }

    /**
     * Restaura registros eliminados e invalida caché
     *
     * @param array $where
     * @return bool
     */
    public function restore($where)
    {
        return $this->update(['deleted' => 0], $where);
    }

    /**
     * Restaura un registro por ID e invalida caché
     *
     * @param int $id
     * @return bool
     */
    public function restore_by_id($id)
    {
        return $this->restore([$this->primary_key => $id]);
    }

    /**
     * Elimina registros permanentemente e invalida caché
     *
     * @param array $where
     * @return bool
     */
    public function hard_delete($where)
    {
        if ($this->db->delete($this->table, $where)) {
            // Invalidar caché
            $this->bump_enhanced_cache_version();
            
            return TRUE;
        }
        
        return FALSE;
    }

    /**
     * Obtiene una lista dropdown con caché
     *
     * @param array $option_fields Campos a mostrar
     * @param string $key_field Campo para la clave
     * @param array $where Condiciones
     * @return array
     */
    public function get_dropdown_list($option_fields = ['name'], $key_field = 'id', $where = [])
    {
        if (!isset($where['deleted'])) {
            $where['deleted'] = 0;
        }

        $params = [
            'option_fields' => $option_fields,
            'key_field' => $key_field,
            'where' => $where
        ];
        
        $cache_key = $this->generate_enhanced_cache_key('get_dropdown_list', $params);

        return $this->get_enhanced_cached_data($cache_key, function() use ($option_fields, $key_field, $where) {
            $list_data = $this->get_where($where)->result();
            $result = [];
            
            foreach ($list_data as $data) {
                $text = '';
                foreach ($option_fields as $field) {
                    if (isset($data->$field)) {
                        $text .= $data->$field . ' ';
                    }
                }
                $result[$data->$key_field] = trim($text);
            }
            
            return $result;
        });
    }

    /**
     * Ejecuta una consulta personalizada con caché
     *
     * @param string $sql
     * @param array $params
     * @param string $cache_key_suffix
     * @param int|null $ttl
     * @return object Proxy object that behaves like CI_DB_result
     */
    public function query_cached($sql, $params = [], $cache_key_suffix = '', $ttl = null)
    {
        $cache_params = [
            'sql' => $sql,
            'params' => $params,
            'suffix' => $cache_key_suffix
        ];
        
        $cache_key = $this->generate_enhanced_cache_key('custom_query', $cache_params);

        $cached_data = $this->get_enhanced_cached_data($cache_key, function() use ($sql, $params) {
            if (!empty($params)) {
                $query = $this->db->query($sql, $params);
            } else {
                $query = $this->db->query($sql);
            }
            return $query->result();
        }, $ttl);

        return $this->create_enhanced_db_result_proxy($cached_data);
    }

    /**
     * Limpia toda la caché de esta tabla
     *
     * @return bool
     */
    public function clear_cache()
    {
        return $this->flush_enhanced_cache();
    }

    /**
     * Obtiene información del estado del caché
     *
     * @return array
     */
    public function get_cache_status()
    {
        return $this->get_enhanced_cache_info();
    }

    /**
     * Cambia el driver de caché dinámicamente
     *
     * @param string $driver_type 'predis' o 'native'
     * @return bool
     */
    public function switch_driver($driver_type)
    {
        return $this->switch_cache_driver($driver_type);
    }
}
