<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class AlertProcessor_library{
    protected $CI; // ci controller (singleton) lo que pasa en la peticion
    protected $cache; // redis
    protected $config; // array de configuracion.

    public function __construct()
    {
        // Obtener instancia de CodeIgniter
        $this->CI =& get_instance();
        
        // Cargar las dependencias necesarias
        $this->CI->load->library('Cache_abstraction_layer');
        $this->CI->load->config('alerts');
        $this->CI->load->helper('general'); // Asegúrate de que el helper esté cargado
    }

    public function processAlerts()
    {
        // Obtener la cola de alertas desde Cache (Predis por defecto)
        $alerts_json = $this->CI->cache_abstraction_layer->get('alerts_queue'); // false
        $alerts = $alerts_json ? json_decode($alerts_json, true) : []; // operador ternario

        // Si no hay alertas, registrar un mensaje en el log
        if (empty($alerts)) {
            log_message('info', 'No se encontraron alertas para procesar.');
            return ['success' => true, 'message' => 'No se encontraron alertas para procesar.'];
        }

        $processed_count = 0;

        // Procesar cada alerta
        foreach ($alerts as $alert) {
            $this->processAlert($alert);
            $processed_count++;
            log_message('info', "Alerta procesada: " . json_encode($alert));
        }

        // Limpiar la cola después de procesar
        $this->CI->cache_abstraction_layer->save('alerts_queue', json_encode([]), 3600);
        log_message('info', "Se procesaron $processed_count alertas y se limpió la cola.");

        return ['success' => true, 'message' => "Se procesaron $processed_count alertas."];
    }

    protected function processAlert($alert)
    {
        // Procesar alertas según su tipo
        switch ($alert['type']) {
            case 'performance':
                $this->processPerformanceAlert($alert);
                break;
            case 'error':
                $this->processErrorAlert($alert);
                break;
            default:
                log_message('error', "Tipo de alerta desconocido: " . json_encode($alert));
        }
    }

    protected function processPerformanceAlert($alert)
    {
        // Convertir imagen a base64
        $image_path = FCPATH . 'assets/images/mimasoft.png'; // Ruta física al archivo
        $image_type = pathinfo($image_path, PATHINFO_EXTENSION);
        $image_base64 = base64_encode(file_get_contents($image_path));
        // Recolectar datos relevantes para la vista
        $data = [
            'url' => $alert['url'],
            'execution_time' => $alert['execution_time'] ?? 'Desconocido',
            'datetime' => date('Y-m-d H:i:s'),
            'memory_usage' => isset($alert['memory_usage']) ? round($alert['memory_usage'] / (1024 * 1024), 2) . ' MB' : 'Desconocido', // Convertir de bytes a MB
            'db_queries' => $alert['db_queries'] ?? '0',
            'response_size' => isset($alert['response_size']) ? round($alert['response_size'] / 1024, 2) . ' KB' : 'Desconocido', // Convertir de bytes a KB
            'loaded_files' => $alert['loaded_files'] ?? 'Desconocido',
            'error' => $alert['error'] ?? null, // Si se detectó algún error relacionado con la alerta de rendimiento
            // Agregar la imagen en base64
            'logo_base64' => $image_base64,
            'logo_type' => $image_type,
            "user_info" => $alert["user_info"] ?? "Desconocido",
        ];

        // Cargar la vista y pasar los datos
        $subject = "Alerta de rendimiento - {$alert['url']}";
        $message = $this->CI->load->view('email_templates/performance_alert', $data, TRUE);
        $recipients = $this->CI->config->item('performance_alert_recipients');

        // Enviar alerta y registrar el resultado
        if ($this->sendAlert($subject, $message, $recipients)) {
            log_message('info', "Correo de alerta de rendimiento enviado para URL: {$alert['url']}");
        } else {
            log_message('error', "Error al enviar correo de alerta de rendimiento para URL: {$alert['url']}");
        }
    }


    protected function processErrorAlert($alert)
    {
        // Filtrar $_SERVER para que solo incluya los datos no sensibles
        $filtered_server_data = $this->filter_server_data($_SERVER);

        // Recolectar datos relevantes para la vista
        $data = [
            'url' => $alert['url'],
            'controller' => $alert['controller'] ?? 'Desconocido',
            'method' => $alert['method'] ?? 'Desconocido',
            'datetime' => date('Y-m-d H:i:s'),
            'error' => $alert['error'] ?? 'No se ha proporcionado un detalle de error',
            'sql_error' => $alert['sql_error'] ?? 'No se han producido errores de SQL',
            'post_data' => !empty($_POST) ? json_encode($_POST, JSON_PRETTY_PRINT) : 'No hay datos POST',
            'get_data' => !empty($_GET) ? json_encode($_GET, JSON_PRETTY_PRINT) : 'No hay datos GET',
            'server_data' => json_encode($filtered_server_data, JSON_PRETTY_PRINT)
        ];

        // Cargar la vista y pasar los datos
        $subject = "Error 500 detectado - {$alert['url']}";
        $message = $this->CI->load->view('email_templates/error_alert', $data, TRUE);
        $recipients = $this->CI->config->item('error_alert_recipients');

        // Enviar alerta y registrar el resultado
        if ($this->sendAlert($subject, $message, $recipients)) {
            log_message('info', "Correo de alerta de error enviado para URL: {$alert['url']}");
        } else {
            log_message('error', "Error al enviar correo de alerta de error para URL: {$alert['url']}");
        }
    }

    /**
     * Filtra los datos de $_SERVER para evitar exponer información sensible.
     * Solo retorna las claves permitidas.
     */
    protected function filter_server_data($server_data)
    {
        // Claves seguras que pueden ser compartidas
        $safe_keys = [
            'HTTP_HOST',
            'REQUEST_URI',
            'REQUEST_METHOD',
            'HTTP_USER_AGENT',
            'SERVER_PROTOCOL',
            'HTTP_REFERER',
            'SERVER_NAME',
        ];

        // Filtrar los datos de $_SERVER para devolver solo los permitidos
        $filtered_data = [];
        foreach ($safe_keys as $key) {
            if (isset($server_data[$key])) {
                $filtered_data[$key] = $server_data[$key];
            }
        }

        return $filtered_data;
    }


    protected function sendAlert($subject, $message, $recipients)
    {
        try {
            if (function_exists('send_app_mail')) {
                // Utilizar la función helper para enviar el correo
                $sent = send_app_mail($recipients['to'], $subject, $message, [
                    'cc' => $recipients['cc'] ?? [],
                    'bcc' => $recipients['bcc'] ?? []
                ]);

                if ($sent) {
                    log_message('info', "Correo enviado utilizando send_app_mail a: " . implode(', ', (array)$recipients['to']));
                } else {
                    log_message('error', "send_app_mail falló al enviar correo a: " . implode(', ', (array)$recipients['to']));
                }
            } else {
                // Alternativa: usar la biblioteca de email de CodeIgniter
                $this->CI->load->library('email');

                $this->CI->email->from(get_setting('email_sent_from_address'), get_setting('email_sent_from_name'));
                $this->CI->email->to($recipients['to']);
                $this->CI->email->subject($subject);
                $this->CI->email->message($message);

                // Añadir CC si existe
                if (!empty($recipients['cc'])) {
                    $this->CI->email->cc($recipients['cc']);
                }

                // Añadir BCC si existe
                if (!empty($recipients['bcc'])) {
                    $this->CI->email->bcc($recipients['bcc']);
                }

                if ($this->CI->email->send()) {
                    log_message('info', "Correo enviado utilizando la biblioteca de email de CodeIgniter a: " . implode(', ', (array)$recipients['to']));
                } else {
                    throw new Exception($this->CI->email->print_debugger());
                }
            }
            return true;
        } catch (Exception $e) {
            log_message('error', "Error al enviar correo: " . $e->getMessage());
            return false;
        }
    }
}
