<?php

defined('BASEPATH') or exit('No direct script access allowed');
// Archivo: application/config/cache.php
// $config['adapter'] = 'memcached';
// $config['backup'] = 'file';

// // Configuración específica de Memcached
// $config['memcached'] = array(
// 'default' => array(
// 'hostname' => 'memcached_server', // Reemplaza con el hostname de tu servidor Memcached
// 'port' => '11211',
// 'weight' => '1',
// ),
// );

// Configuración principal del driver de cache
$config['cache_driver'] = 'redis';  // Usar Redis como caché
$config['cache_backup']  = 'file';   // Respaldo en archivo en caso de fallo de Redis

// Habilitar Predis como reemplazo del driver nativo de Redis
// TRUE = usar Predis, FALSE = usar driver nativo de CodeIgniter
$config['use_predis_cache'] = TRUE;

// Configuración específica de Redis
$config['redis'] = array(
    'socket_type' => 'tcp',            // 'tcp' o 'unix'
    'host'        => '127.0.0.1',   // Nombre del servicio de Redis en Docker Compose
    'password'    => 'tHjGQeXrgmI45Y', // Contraseña de Redis
    'port'        => 6379,             // Puerto de Redis
    'timeout'     => 0                 // Timeout de la conexión
);

// Tiempo de vida por defecto de los datos en caché (en segundos)
$config['cache_expiration'] = 3600;    // 1 hora