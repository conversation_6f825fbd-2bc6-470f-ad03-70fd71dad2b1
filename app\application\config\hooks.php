<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| Hooks
| -------------------------------------------------------------------------
| This file lets you define "hooks" to extend CI without hacking the core
| files.  Please see the user guide for info:
|
|	http://codeigniter.com/user_guide/general/hooks.html
|
*/

$hook['post_controller_constructor'][] = array(
    'class' => 'LanguageLoader',
    'function' => 'initialize',
    'filename' => 'LanguageLoader.php',
    'filepath' => 'hooks'
);

// Hook para compatibilidad de caché v2.0
$hook['post_controller_constructor'][] = array(
    'class' => 'Cache_compatibility_hook',
    'function' => 'initialize_cache_compatibility',
    'filename' => 'Cache_compatibility_hook.php',
    'filepath' => 'hooks'
);


$hook['pre_system'] = array(
    'class'    => 'PerformanceHook',
    'function' => 'pre_system',
    'filename' => 'PerformanceHook.php',
    'filepath' => 'hooks',
    'params'   => array()
);

$hook['post_system'][] = array(
    'class'    => 'PerformanceHook',
    'function' => 'post_system',
    'filename' => 'PerformanceHook.php',
    'filepath' => 'hooks',
    'params'   => array()
);

$hook['post_system'][] = array(
    'class'    => 'ErrorHook',
    'function' => 'handle_error',
    'filename' => 'ErrorHook.php',
    'filepath' => 'hooks',
    'params'   => array()
);
/*
$hook['post_controller_constructor'] = array(
    'class' => '',
    'function' => 'init_settings',
    'filename' => 'init_settings.php',
    'filepath' => 'hooks'
);
*/