<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Cache Compatibility Helper
 *
 * Helper para mantener compatibilidad con código que usa la sintaxis antigua
 * de acceso a Redis ($this->cache->redis)
 *
 * @package    CodeIgniter
 * @subpackage Helpers
 * @category   Cache
 * <AUTHOR> Pro Team
 * @version    2.0.0
 */

if (!function_exists('get_cache_instance')) {
    /**
     * Obtiene una instancia de caché compatible
     * 
     * Esta función proporciona una instancia que mantiene compatibilidad
     * con el código existente que usa $this->cache->redis
     *
     * @return object Instancia de caché compatible
     */
    function get_cache_instance()
    {
        $CI =& get_instance();
        
        // Si ya existe la capa de abstracción, usarla
        if (!isset($CI->cache_abstraction_layer)) {
            $CI->load->library('Cache_abstraction_layer');
        }
        
        // Crear un wrapper que emule la estructura antigua
        return new Cache_compatibility_wrapper($CI->cache_abstraction_layer);
    }
}

if (!function_exists('migrate_cache_call')) {
    /**
     * Migra automáticamente llamadas de caché antiguas
     *
     * @param string $method Método a llamar
     * @param array $args Argumentos del método
     * @return mixed Resultado del método
     */
    function migrate_cache_call($method, $args = [])
    {
        $CI =& get_instance();
        
        if (!isset($CI->cache_abstraction_layer)) {
            $CI->load->library('Cache_abstraction_layer');
        }
        
        // Log de migración automática
        log_message('info', "Cache Compatibility: Migrating call to $method");
        
        return call_user_func_array([$CI->cache_abstraction_layer, $method], $args);
    }
}

/**
 * Wrapper de compatibilidad para mantener la estructura $cache->redis
 */
class Cache_compatibility_wrapper
{
    protected $abstraction_layer;
    public $redis;
    
    public function __construct($abstraction_layer)
    {
        $this->abstraction_layer = $abstraction_layer;
        
        // Crear un objeto redis que redirija a la capa de abstracción
        $this->redis = new Redis_compatibility_wrapper($abstraction_layer);
    }
    
    // Delegar métodos directos a la capa de abstracción
    public function __call($method, $args)
    {
        return call_user_func_array([$this->abstraction_layer, $method], $args);
    }
    
    public function get($key)
    {
        return $this->abstraction_layer->get($key);
    }
    
    public function save($key, $value, $ttl = 60)
    {
        return $this->abstraction_layer->save($key, $value, $ttl);
    }
    
    public function delete($key)
    {
        return $this->abstraction_layer->delete($key);
    }
}

/**
 * Wrapper específico para mantener compatibilidad con $cache->redis
 */
class Redis_compatibility_wrapper
{
    protected $abstraction_layer;
    
    public function __construct($abstraction_layer)
    {
        $this->abstraction_layer = $abstraction_layer;
    }
    
    public function get($key)
    {
        log_message('info', "Cache Compatibility: Redirecting redis->get('$key') to abstraction layer");
        return $this->abstraction_layer->get($key);
    }
    
    public function save($key, $value, $ttl = 60)
    {
        log_message('info', "Cache Compatibility: Redirecting redis->save('$key') to abstraction layer");
        return $this->abstraction_layer->save($key, $value, $ttl);
    }
    
    public function delete($key)
    {
        log_message('info', "Cache Compatibility: Redirecting redis->delete('$key') to abstraction layer");
        return $this->abstraction_layer->delete($key);
    }
    
    public function is_supported()
    {
        $driver_info = $this->abstraction_layer->get_driver_info();
        return $driver_info['initialized'];
    }
    
    public function increment($key, $offset = 1)
    {
        log_message('info', "Cache Compatibility: Redirecting redis->increment('$key') to abstraction layer");
        return $this->abstraction_layer->increment($key, $offset);
    }
    
    public function decrement($key, $offset = 1)
    {
        log_message('info', "Cache Compatibility: Redirecting redis->decrement('$key') to abstraction layer");
        return $this->abstraction_layer->decrement($key, $offset);
    }
    
    public function clean()
    {
        log_message('info', "Cache Compatibility: Redirecting redis->clean() to abstraction layer");
        return $this->abstraction_layer->clean();
    }
    
    // Método mágico para capturar cualquier otro método
    public function __call($method, $args)
    {
        log_message('info', "Cache Compatibility: Redirecting redis->$method() to abstraction layer");
        
        if (method_exists($this->abstraction_layer, $method)) {
            return call_user_func_array([$this->abstraction_layer, $method], $args);
        }
        
        log_message('error', "Cache Compatibility: Method redis->$method() not found in abstraction layer");
        return false;
    }
}
