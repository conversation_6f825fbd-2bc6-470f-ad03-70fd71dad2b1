<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Debug Cache Controller
 * 
 * Controlador simple para diagnosticar problemas de acceso
 */
class Debug_cache extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
    }

    public function index()
    {
        echo "<h1>Debug Cache Controller</h1>";
        echo "<p>Este controlador está funcionando correctamente.</p>";
        echo "<p><strong>Entorno actual:</strong> " . ENVIRONMENT . "</p>";
        echo "<p><strong>Base URL:</strong> " . base_url() . "</p>";
        echo "<p><strong>URI:</strong> " . uri_string() . "</p>";
        
        echo "<h2>Enlaces de prueba:</h2>";
        echo "<p><a href='" . base_url('debug_cache/test_cache_migration') . "'>Test Cache Migration</a></p>";
        echo "<p><a href='" . base_url('debug_cache/test_cache_test') . "'>Test Cache Test</a></p>";
        echo "<p><a href='" . base_url('debug_cache/environment_info') . "'>Environment Info</a></p>";
    }

    public function test_cache_migration()
    {
        echo "<h1>Test Cache Migration</h1>";
        
        // Verificar si el archivo existe
        $file_path = APPPATH . 'controllers/Cache_migration.php';
        echo "<p><strong>Archivo Cache_migration.php:</strong> " . (file_exists($file_path) ? 'Existe' : 'No existe') . "</p>";
        
        if (file_exists($file_path)) {
            echo "<p>Intentando cargar el controlador...</p>";
            try {
                // Intentar incluir el archivo
                include_once $file_path;
                echo "<p>✅ Archivo incluido correctamente</p>";
                
                if (class_exists('Cache_migration')) {
                    echo "<p>✅ Clase Cache_migration existe</p>";
                    echo "<p><a href='" . base_url('cache_migration') . "'>Ir a Cache Migration</a></p>";
                } else {
                    echo "<p>❌ Clase Cache_migration no encontrada</p>";
                }
            } catch (Exception $e) {
                echo "<p>❌ Error al incluir archivo: " . $e->getMessage() . "</p>";
            }
        }
        
        echo "<p><a href='" . base_url('debug_cache') . "'>← Volver</a></p>";
    }

    public function test_cache_test()
    {
        echo "<h1>Test Cache Test</h1>";
        
        // Verificar si el archivo existe
        $file_path = APPPATH . 'controllers/Cache_test.php';
        echo "<p><strong>Archivo Cache_test.php:</strong> " . (file_exists($file_path) ? 'Existe' : 'No existe') . "</p>";
        
        if (file_exists($file_path)) {
            echo "<p>Intentando cargar el controlador...</p>";
            try {
                // Intentar incluir el archivo
                include_once $file_path;
                echo "<p>✅ Archivo incluido correctamente</p>";
                
                if (class_exists('Cache_test')) {
                    echo "<p>✅ Clase Cache_test existe</p>";
                    echo "<p><a href='" . base_url('cache_test') . "'>Ir a Cache Test</a></p>";
                } else {
                    echo "<p>❌ Clase Cache_test no encontrada</p>";
                }
            } catch (Exception $e) {
                echo "<p>❌ Error al incluir archivo: " . $e->getMessage() . "</p>";
            }
        }
        
        echo "<p><a href='" . base_url('debug_cache') . "'>← Volver</a></p>";
    }

    public function environment_info()
    {
        echo "<h1>Environment Information</h1>";
        
        echo "<h2>CodeIgniter Info</h2>";
        echo "<pre>";
        echo "ENVIRONMENT: " . ENVIRONMENT . "\n";
        echo "BASEPATH: " . BASEPATH . "\n";
        echo "APPPATH: " . APPPATH . "\n";
        echo "FCPATH: " . FCPATH . "\n";
        echo "Base URL: " . base_url() . "\n";
        echo "Index page: " . $this->config->item('index_page') . "\n";
        echo "</pre>";
        
        echo "<h2>Server Info</h2>";
        echo "<pre>";
        echo "HTTP_HOST: " . (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'Not set') . "\n";
        echo "REQUEST_URI: " . (isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : 'Not set') . "\n";
        echo "SCRIPT_NAME: " . (isset($_SERVER['SCRIPT_NAME']) ? $_SERVER['SCRIPT_NAME'] : 'Not set') . "\n";
        echo "DOCUMENT_ROOT: " . (isset($_SERVER['DOCUMENT_ROOT']) ? $_SERVER['DOCUMENT_ROOT'] : 'Not set') . "\n";
        echo "</pre>";
        
        echo "<h2>Controllers Directory</h2>";
        $controllers_dir = APPPATH . 'controllers/';
        if (is_dir($controllers_dir)) {
            $files = scandir($controllers_dir);
            echo "<pre>";
            foreach ($files as $file) {
                if ($file != '.' && $file != '..' && strpos($file, 'Cache') !== false) {
                    echo "Found: $file\n";
                }
            }
            echo "</pre>";
        }
        
        echo "<h2>Routes</h2>";
        echo "<pre>";
        $this->load->config('routes');
        $routes = $this->config->item('routes');
        if ($routes) {
            print_r($routes);
        } else {
            echo "No custom routes found\n";
        }
        echo "</pre>";
        
        echo "<p><a href='" . base_url('debug_cache') . "'>← Volver</a></p>";
    }

    public function fix_environment()
    {
        echo "<h1>Fix Environment</h1>";
        
        // Verificar y mostrar el entorno actual
        echo "<p><strong>Entorno actual:</strong> " . ENVIRONMENT . "</p>";
        
        if (ENVIRONMENT === 'production') {
            echo "<p>⚠️ El entorno está configurado como 'production'.</p>";
            echo "<p>Los controladores Cache_migration y Cache_test solo funcionan en 'development'.</p>";
            
            echo "<h2>Soluciones:</h2>";
            echo "<ol>";
            echo "<li><strong>Cambiar .htaccess:</strong> Cambiar 'SetEnv CI_ENVIRONMENT production' a 'SetEnv CI_ENVIRONMENT development'</li>";
            echo "<li><strong>O usar index.php:</strong> Acceder a <a href='" . base_url('index.php/cache_migration') . "'>index.php/cache_migration</a></li>";
            echo "<li><strong>O usar este debug:</strong> <a href='" . base_url('debug_cache/force_cache_migration') . "'>Forzar Cache Migration</a></li>";
            echo "</ol>";
        } else {
            echo "<p>✅ El entorno está configurado correctamente para desarrollo.</p>";
        }
        
        echo "<p><a href='" . base_url('debug_cache') . "'>← Volver</a></p>";
    }

    public function force_cache_migration()
    {
        echo "<h1>Force Cache Migration</h1>";
        echo "<p>Intentando cargar Cache_migration directamente...</p>";
        
        try {
            // Forzar la carga del controlador
            require_once APPPATH . 'controllers/Cache_migration.php';
            
            // Crear instancia temporal para mostrar el contenido
            $cache_migration = new Cache_migration();
            
            echo "<div style='border: 1px solid #ccc; padding: 20px; margin: 20px 0;'>";
            echo "<h2>Cache Migration (Forzado)</h2>";
            
            // Simular el método index
            $use_predis = $this->config->item('use_predis_cache');
            $driver_name = $use_predis ? 'Predis' : 'Driver Nativo CI';
            
            echo "<p><strong>Estado actual:</strong> $driver_name</p>";
            echo "<hr>";
            
            echo "<h3>Acciones de Migración</h3>";
            echo "<p><a href='" . base_url('debug_cache/enable_predis') . "' style='color: green;'>✅ Habilitar Predis</a></p>";
            echo "<p><a href='" . base_url('debug_cache/disable_predis') . "' style='color: red;'>❌ Usar Driver Nativo</a></p>";
            echo "<hr>";
            
            echo "<h3>Tests Básicos</h3>";
            echo "<p><a href='" . base_url('debug_cache/basic_cache_test') . "'>🧪 Test Básico de Caché</a></p>";
            
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<p>❌ Error: " . $e->getMessage() . "</p>";
        }
        
        echo "<p><a href='" . base_url('debug_cache') . "'>← Volver</a></p>";
    }

    public function enable_predis()
    {
        $this->update_cache_config(TRUE);
        echo "<h2>✅ Predis Habilitado</h2>";
        echo "<p>El sistema ahora usa Predis como driver de caché.</p>";
        echo "<p><a href='" . base_url('debug_cache/force_cache_migration') . "'>← Volver</a></p>";
    }

    public function disable_predis()
    {
        $this->update_cache_config(FALSE);
        echo "<h2>❌ Driver Nativo Habilitado</h2>";
        echo "<p>El sistema ahora usa el driver nativo de CodeIgniter.</p>";
        echo "<p><a href='" . base_url('debug_cache/force_cache_migration') . "'>← Volver</a></p>";
    }

    public function basic_cache_test()
    {
        echo "<h2>🧪 Test Básico de Caché</h2>";
        
        $use_predis = $this->config->item('use_predis_cache');
        $driver_name = $use_predis ? 'Predis' : 'Driver Nativo CI';
        
        echo "<p><strong>Driver actual:</strong> $driver_name</p>";
        
        try {
            // Cargar caché
            $this->load->driver('cache', [
                'adapter' => $this->config->item('cache_driver'),
                'backup'  => $this->config->item('cache_backup'),
                'redis'   => $this->config->item('redis')
            ]);
            
            // Test básico
            $test_key = 'debug_test_' . time();
            $test_value = 'Test value from debug controller';
            
            $save_result = $this->cache->save($test_key, $test_value, 300);
            $get_result = $this->cache->get($test_key);
            $delete_result = $this->cache->delete($test_key);
            
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Operación</th><th>Resultado</th></tr>";
            echo "<tr><td>Save</td><td>" . ($save_result ? '✅ OK' : '❌ FAIL') . "</td></tr>";
            echo "<tr><td>Get</td><td>" . ($get_result === $test_value ? '✅ OK' : '❌ FAIL') . "</td></tr>";
            echo "<tr><td>Delete</td><td>" . ($delete_result ? '✅ OK' : '❌ FAIL') . "</td></tr>";
            echo "</table>";
            
        } catch (Exception $e) {
            echo "<p>❌ Error: " . $e->getMessage() . "</p>";
        }
        
        echo "<p><a href='" . base_url('debug_cache/force_cache_migration') . "'>← Volver</a></p>";
    }

    private function update_cache_config($use_predis)
    {
        $config_file = APPPATH . 'config/cache.php';
        $content = file_get_contents($config_file);
        
        if ($use_predis) {
            $content = preg_replace(
                '/\$config\[\'use_predis_cache\'\]\s*=\s*FALSE;/',
                '$config[\'use_predis_cache\'] = TRUE;',
                $content
            );
        } else {
            $content = preg_replace(
                '/\$config\[\'use_predis_cache\'\]\s*=\s*TRUE;/',
                '$config[\'use_predis_cache\'] = FALSE;',
                $content
            );
        }
        
        file_put_contents($config_file, $content);
        
        // Recargar configuración
        $this->config->load('cache', TRUE);
    }
}
