<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Cache Migration Controller
 *
 * Controlador para gestionar la migración entre el driver nativo de Redis y Predis
 * Permite alternar entre ambos sistemas y realizar pruebas comparativas
 *
 * @package    CodeIgniter
 * @subpackage Controllers
 * @category   Migration
 * <AUTHOR> Pro Team
 * @version    1.0.0
 */
class Cache_migration extends CI_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();

        // Solo permitir en desarrollo o si se accede con parámetro especial
        if (ENVIRONMENT !== 'development' && !$this->input->get('allow_access')) {
            echo "<h1>Acceso Restringido</h1>";
            echo "<p>Este controlador solo está disponible en entorno de desarrollo.</p>";
            echo "<p><strong>Entorno actual:</strong> " . ENVIRONMENT . "</p>";
            echo "<p><strong>Soluciones:</strong></p>";
            echo "<ul>";
            echo "<li>Cambiar el entorno a 'development' en .htaccess</li>";
            echo "<li>O acceder con: <a href='" . current_url() . "?allow_access=1'>?allow_access=1</a></li>";
            echo "<li>O usar el controlador debug: <a href='" . base_url('debug_cache') . "'>Debug Cache</a></li>";
            echo "</ul>";
            return;
        }
    }

    /**
     * Página principal de migración
     */
    public function index()
    {
        $current_status = $this->config->item('use_predis_cache');
        $driver_name = $current_status ? 'Predis' : 'Driver Nativo CI';
        
        echo "<h1>Migración Redis → Predis</h1>";
        echo "<p><strong>Estado actual:</strong> $driver_name</p>";
        echo "<hr>";
        
        echo "<h2>Acciones de Migración</h2>";
        echo "<p><a href='" . site_url('cache_migration/enable_predis') . "' style='color: green;'>✅ Habilitar Predis</a></p>";
        echo "<p><a href='" . site_url('cache_migration/disable_predis') . "' style='color: red;'>❌ Usar Driver Nativo</a></p>";
        echo "<hr>";
        
        echo "<h2>Tests y Verificación</h2>";
        echo "<p><a href='" . site_url('cache_migration/test_current_driver') . "'>🧪 Probar Driver Actual</a></p>";
        echo "<p><a href='" . site_url('cache_migration/compare_drivers') . "'>⚖️ Comparar Ambos Drivers</a></p>";
        echo "<p><a href='" . site_url('cache_migration/test_crud_models') . "'>📊 Probar Modelos CRUD</a></p>";
        echo "<hr>";
        
        echo "<h2>Información del Sistema</h2>";
        echo "<p><a href='" . site_url('cache_migration/system_info') . "'>ℹ️ Información del Sistema</a></p>";
        echo "<p><a href='" . site_url('cache_test') . "'>🔗 Tests Completos</a></p>";
    }

    /**
     * Habilita Predis como driver de caché
     */
    public function enable_predis()
    {
        $this->update_cache_config(TRUE);
        echo "<h2>✅ Predis Habilitado</h2>";
        echo "<p>El sistema ahora usa Predis como driver de caché.</p>";
        echo "<p><a href='" . site_url('cache_migration') . "'>← Volver</a></p>";
    }

    /**
     * Deshabilita Predis y usa el driver nativo
     */
    public function disable_predis()
    {
        $this->update_cache_config(FALSE);
        echo "<h2>❌ Driver Nativo Habilitado</h2>";
        echo "<p>El sistema ahora usa el driver nativo de CodeIgniter.</p>";
        echo "<p><a href='" . site_url('cache_migration') . "'>← Volver</a></p>";
    }

    /**
     * Prueba el driver actual
     */
    public function test_current_driver()
    {
        $use_predis = $this->config->item('use_predis_cache');
        $driver_name = $use_predis ? 'Predis' : 'Driver Nativo CI';
        
        echo "<h2>🧪 Prueba del Driver Actual: $driver_name</h2>";
        
        $results = [];
        
        try {
            // Inicializar caché según configuración actual
            if ($use_predis) {
                $this->load->library('Predis_cache_driver');
                $cache = new Predis_cache_adapter([
                    'adapter' => $this->config->item('cache_driver'),
                    'backup'  => $this->config->item('cache_backup'),
                    'redis'   => $this->config->item('redis')
                ]);
            } else {
                $this->load->driver('cache', [
                    'adapter' => $this->config->item('cache_driver'),
                    'backup'  => $this->config->item('cache_backup'),
                    'redis'   => $this->config->item('redis')
                ]);
                $cache = $this->cache;
            }
            
            // Test básico
            $test_key = 'migration_test_' . time();
            $test_value = 'Test value for ' . $driver_name;
            
            $save_result = $cache->save($test_key, $test_value, 300);
            $get_result = $cache->get($test_key);
            $delete_result = $cache->delete($test_key);
            
            $results[] = [
                'test' => 'Operaciones Básicas',
                'success' => $save_result && $get_result === $test_value && $delete_result,
                'details' => "Save: $save_result, Get: '$get_result', Delete: $delete_result"
            ];
            
            // Test de serialización
            $array_key = 'migration_array_' . time();
            $array_value = ['driver' => $driver_name, 'timestamp' => time()];
            
            $save_array = $cache->save($array_key, $array_value, 300);
            $get_array = $cache->get($array_key);
            $cache->delete($array_key);
            
            $results[] = [
                'test' => 'Serialización',
                'success' => $save_array && $get_array === $array_value,
                'details' => "Array guardado y recuperado correctamente"
            ];
            
            // Test de performance
            $start_time = microtime(true);
            for ($i = 0; $i < 100; $i++) {
                $cache->save("perf_test_$i", "value_$i", 300);
            }
            $write_time = microtime(true) - $start_time;
            
            $start_time = microtime(true);
            for ($i = 0; $i < 100; $i++) {
                $cache->get("perf_test_$i");
            }
            $read_time = microtime(true) - $start_time;
            
            // Limpiar
            for ($i = 0; $i < 100; $i++) {
                $cache->delete("perf_test_$i");
            }
            
            $results[] = [
                'test' => 'Performance (100 ops)',
                'success' => true,
                'details' => "Write: {$write_time}s, Read: {$read_time}s"
            ];
            
        } catch (Exception $e) {
            $results[] = [
                'test' => 'Error General',
                'success' => false,
                'details' => $e->getMessage()
            ];
        }
        
        $this->display_test_results($results);
        echo "<p><a href='" . site_url('cache_migration') . "'>← Volver</a></p>";
    }

    /**
     * Compara ambos drivers
     */
    public function compare_drivers()
    {
        echo "<h2>⚖️ Comparación de Drivers</h2>";
        
        $results = [];
        
        // Test con driver nativo
        echo "<h3>Probando Driver Nativo...</h3>";
        $this->update_cache_config(FALSE);
        $native_results = $this->run_performance_test('Driver Nativo');
        
        // Test con Predis
        echo "<h3>Probando Predis...</h3>";
        $this->update_cache_config(TRUE);
        $predis_results = $this->run_performance_test('Predis');
        
        // Mostrar comparación
        echo "<h3>Resultados de Comparación</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Métrica</th><th>Driver Nativo</th><th>Predis</th><th>Diferencia</th></tr>";
        
        foreach ($native_results as $key => $native_value) {
            $predis_value = $predis_results[$key];
            $difference = '';
            
            if (is_numeric($native_value) && is_numeric($predis_value)) {
                $diff_percent = (($predis_value - $native_value) / $native_value) * 100;
                $difference = sprintf("%.2f%%", $diff_percent);
                if ($diff_percent < 0) {
                    $difference = "<span style='color: green;'>$difference (mejor)</span>";
                } else {
                    $difference = "<span style='color: red;'>+$difference (peor)</span>";
                }
            }
            
            echo "<tr>";
            echo "<td>$key</td>";
            echo "<td>$native_value</td>";
            echo "<td>$predis_value</td>";
            echo "<td>$difference</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        echo "<p><a href='" . site_url('cache_migration') . "'>← Volver</a></p>";
    }

    /**
     * Prueba modelos CRUD
     */
    public function test_crud_models()
    {
        echo "<h2>📊 Prueba de Modelos CRUD</h2>";
        
        $use_predis = $this->config->item('use_predis_cache');
        $driver_name = $use_predis ? 'Predis' : 'Driver Nativo CI';
        
        echo "<p><strong>Driver actual:</strong> $driver_name</p>";
        
        $results = [];
        
        try {
            // Test con Users_model
            $start_time = microtime(true);
            $users1 = $this->Users_model->get_all();
            $time1 = microtime(true) - $start_time;
            
            $start_time = microtime(true);
            $users2 = $this->Users_model->get_all();
            $time2 = microtime(true) - $start_time;
            
            $results[] = [
                'test' => 'Users Model Cache',
                'success' => $users1->num_rows() === $users2->num_rows() && $time2 < $time1,
                'details' => "Primera consulta: {$time1}s, Segunda: {$time2}s, Filas: " . $users1->num_rows()
            ];
            
            // Test con Projects_model si existe
            if (class_exists('Projects_model')) {
                $start_time = microtime(true);
                $projects1 = $this->Projects_model->get_all();
                $time1 = microtime(true) - $start_time;
                
                $start_time = microtime(true);
                $projects2 = $this->Projects_model->get_all();
                $time2 = microtime(true) - $start_time;
                
                $results[] = [
                    'test' => 'Projects Model Cache',
                    'success' => $projects1->num_rows() === $projects2->num_rows() && $time2 < $time1,
                    'details' => "Primera consulta: {$time1}s, Segunda: {$time2}s, Filas: " . $projects1->num_rows()
                ];
            }
            
        } catch (Exception $e) {
            $results[] = [
                'test' => 'Error en Modelos CRUD',
                'success' => false,
                'details' => $e->getMessage()
            ];
        }
        
        $this->display_test_results($results);
        echo "<p><a href='" . site_url('cache_migration') . "'>← Volver</a></p>";
    }

    /**
     * Muestra información del sistema
     */
    public function system_info()
    {
        echo "<h2>ℹ️ Información del Sistema</h2>";
        
        echo "<h3>Configuración Actual</h3>";
        echo "<pre>";
        echo "use_predis_cache: " . var_export($this->config->item('use_predis_cache'), true) . "\n";
        echo "cache_driver: " . $this->config->item('cache_driver') . "\n";
        echo "cache_backup: " . $this->config->item('cache_backup') . "\n";
        echo "Redis config: " . json_encode($this->config->item('redis'), JSON_PRETTY_PRINT) . "\n";
        echo "</pre>";
        
        echo "<h3>Extensiones PHP</h3>";
        echo "<pre>";
        echo "Redis extension: " . (extension_loaded('redis') ? 'Instalada' : 'No instalada') . "\n";
        echo "Predis disponible: " . (class_exists('Predis\\Client') ? 'Sí' : 'No') . "\n";
        echo "</pre>";
        
        if ($this->config->item('use_predis_cache')) {
            echo "<h3>Estadísticas Predis</h3>";
            try {
                $this->load->library('Predis_cache_service');
                $stats = $this->predis_cache_service->get_stats();
                echo "<pre>" . json_encode($stats, JSON_PRETTY_PRINT) . "</pre>";
            } catch (Exception $e) {
                echo "<p>Error obteniendo estadísticas: " . $e->getMessage() . "</p>";
            }
        }
        
        echo "<p><a href='" . site_url('cache_migration') . "'>← Volver</a></p>";
    }

    /**
     * Actualiza la configuración de caché
     */
    private function update_cache_config($use_predis)
    {
        $config_file = APPPATH . 'config/cache.php';
        $content = file_get_contents($config_file);
        
        if ($use_predis) {
            $content = preg_replace(
                '/\$config\[\'use_predis_cache\'\]\s*=\s*FALSE;/',
                '$config[\'use_predis_cache\'] = TRUE;',
                $content
            );
        } else {
            $content = preg_replace(
                '/\$config\[\'use_predis_cache\'\]\s*=\s*TRUE;/',
                '$config[\'use_predis_cache\'] = FALSE;',
                $content
            );
        }
        
        file_put_contents($config_file, $content);
        
        // Recargar configuración
        $this->config->load('cache', TRUE);
    }

    /**
     * Ejecuta test de performance
     */
    private function run_performance_test($driver_name)
    {
        $iterations = 50;
        $results = [];
        
        try {
            // Inicializar caché según configuración actual
            $use_predis = $this->config->item('use_predis_cache');
            
            if ($use_predis) {
                $this->load->library('Predis_cache_driver');
                $cache = new Predis_cache_adapter([
                    'adapter' => $this->config->item('cache_driver'),
                    'backup'  => $this->config->item('cache_backup'),
                    'redis'   => $this->config->item('redis')
                ]);
            } else {
                $this->load->driver('cache', [
                    'adapter' => $this->config->item('cache_driver'),
                    'backup'  => $this->config->item('cache_backup'),
                    'redis'   => $this->config->item('redis')
                ]);
                $cache = $this->cache;
            }
            
            // Test de escritura
            $start_time = microtime(true);
            for ($i = 0; $i < $iterations; $i++) {
                $cache->save("perf_$i", "value_$i", 300);
            }
            $write_time = microtime(true) - $start_time;
            
            // Test de lectura
            $start_time = microtime(true);
            for ($i = 0; $i < $iterations; $i++) {
                $cache->get("perf_$i");
            }
            $read_time = microtime(true) - $start_time;
            
            // Limpiar
            for ($i = 0; $i < $iterations; $i++) {
                $cache->delete("perf_$i");
            }
            
            $results['Tiempo Escritura (s)'] = round($write_time, 4);
            $results['Tiempo Lectura (s)'] = round($read_time, 4);
            $results['Ops/seg Escritura'] = round($iterations / $write_time, 2);
            $results['Ops/seg Lectura'] = round($iterations / $read_time, 2);
            
        } catch (Exception $e) {
            $results['Error'] = $e->getMessage();
        }
        
        return $results;
    }

    /**
     * Muestra los resultados de los tests
     */
    private function display_test_results($results)
    {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Test</th><th>Resultado</th><th>Detalles</th></tr>";
        
        foreach ($results as $result) {
            $status = $result['success'] ? '✅ PASS' : '❌ FAIL';
            $color = $result['success'] ? 'green' : 'red';
            
            echo "<tr>";
            echo "<td>{$result['test']}</td>";
            echo "<td style='color: $color; font-weight: bold;'>$status</td>";
            echo "<td>{$result['details']}</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
}
