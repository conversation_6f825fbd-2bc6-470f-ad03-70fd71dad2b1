<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Demo Users Model
 *
 * Modelo de demostración que extiende Enhanced_base_model
 * Muestra cómo cualquier modelo puede usar el sistema de caché v2.0
 */
class Demo_users_model extends Enhanced_base_model
{
    public function __construct()
    {
        parent::__construct();
        
        // Configurar la tabla y opciones de caché
        $this->use_table('users', [
            'cache_time' => 600,        // 10 minutos
            'cache_prefix' => 'demo',   // Prefijo personalizado
            'cache_enabled' => true     // Habilitar caché
        ]);
    }

    /**
     * Obtiene usuarios activos con caché
     */
    public function get_active_users()
    {
        return $this->get_where(['active' => 1, 'deleted' => 0]);
    }

    /**
     * Obtiene usuarios por rol con caché
     */
    public function get_users_by_role($role_id)
    {
        return $this->get_where(['role_id' => $role_id]);
    }

    /**
     * Método para testing del proxy class
     */
    public function create_test_proxy($test_data)
    {
        return $this->create_enhanced_db_result_proxy($test_data);
    }

    /**
     * Obtiene estadísticas de usuarios con caché de larga duración
     */
    public function get_user_stats()
    {
        $cache_key = $this->generate_enhanced_cache_key('get_user_stats', []);
        
        return $this->get_enhanced_cached_data($cache_key, function() {
            // Simular consulta compleja
            return [
                'total_users' => rand(100, 1000),
                'active_users' => rand(50, 500),
                'new_users_today' => rand(1, 20),
                'generated_at' => date('Y-m-d H:i:s')
            ];
        }, 3600); // 1 hora de caché
    }
}
