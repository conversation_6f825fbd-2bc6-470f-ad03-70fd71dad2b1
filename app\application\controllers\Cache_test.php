<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Cache Test Controller
 *
 * Controlador para probar la migración de Redis nativo a Predis
 * Verifica que todas las funcionalidades de caché funcionan correctamente
 *
 * @package    CodeIgniter
 * @subpackage Controllers
 * @category   Testing
 * <AUTHOR> Pro Team
 * @version    1.0.0
 */
class Cache_test extends CI_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        
        // Solo permitir en desarrollo
        if (ENVIRONMENT !== 'development') {
            show_404();
        }
        
        $this->load->library('Cache_service');
        $this->load->model('Test_cache_model');
    }

    /**
     * Página principal de tests
     */
    public function index()
    {
        echo "<h1>Tests de Migración Redis → Predis</h1>";
        echo "<p><a href='" . site_url('cache_test/test_basic_operations') . "'>Test Operaciones Básicas</a></p>";
        echo "<p><a href='" . site_url('cache_test/test_cache_service') . "'>Test Cache Service</a></p>";
        echo "<p><a href='" . site_url('cache_test/test_cacheable_trait') . "'>Test CacheableTrait</a></p>";
        echo "<p><a href='" . site_url('cache_test/test_crud_model') . "'>Test Crud Model</a></p>";
        echo "<p><a href='" . site_url('cache_test/test_performance') . "'>Test Performance</a></p>";
        echo "<p><a href='" . site_url('cache_test/test_compatibility') . "'>Test Compatibilidad Completa</a></p>";
        echo "<p><a href='" . site_url('cache_test/get_stats') . "'>Estadísticas Redis</a></p>";
    }

    /**
     * Test de operaciones básicas de caché
     */
    public function test_basic_operations()
    {
        echo "<h2>Test Operaciones Básicas</h2>";
        
        // Inicializar caché
        $this->load->driver('cache', [
            'adapter' => $this->config->item('cache_driver'),
            'backup'  => $this->config->item('cache_backup'),
            'redis'   => $this->config->item('redis')
        ]);

        $results = [];
        
        // Test 1: Save y Get básico
        $key = 'test_basic_' . time();
        $value = 'Hello Predis!';
        $save_result = $this->cache->save($key, $value, 300);
        $get_result = $this->cache->get($key);
        
        $results[] = [
            'test' => 'Save/Get String',
            'success' => $save_result && $get_result === $value,
            'details' => "Saved: $save_result, Retrieved: '$get_result'"
        ];

        // Test 2: Save y Get Array
        $key_array = 'test_array_' . time();
        $array_value = ['name' => 'Test', 'id' => 123, 'active' => true];
        $save_array = $this->cache->save($key_array, $array_value, 300);
        $get_array = $this->cache->get($key_array);
        
        $results[] = [
            'test' => 'Save/Get Array',
            'success' => $save_array && $get_array === $array_value,
            'details' => "Saved: $save_array, Retrieved: " . json_encode($get_array)
        ];

        // Test 3: Save y Get Object
        $key_object = 'test_object_' . time();
        $object_value = (object)['name' => 'Test Object', 'timestamp' => time()];
        $save_object = $this->cache->save($key_object, $object_value, 300);
        $get_object = $this->cache->get($key_object);
        
        $results[] = [
            'test' => 'Save/Get Object',
            'success' => $save_object && $get_object == $object_value,
            'details' => "Saved: $save_object, Retrieved: " . json_encode($get_object)
        ];

        // Test 4: Increment
        $key_counter = 'test_counter_' . time();
        $this->cache->save($key_counter, 10, 300);
        $increment_result = $this->cache->increment($key_counter, 5);
        $counter_value = $this->cache->get($key_counter);
        
        $results[] = [
            'test' => 'Increment',
            'success' => $increment_result == 15 && $counter_value == 15,
            'details' => "Increment result: $increment_result, Final value: $counter_value"
        ];

        // Test 5: Delete
        $delete_result = $this->cache->delete($key);
        $get_after_delete = $this->cache->get($key);
        
        $results[] = [
            'test' => 'Delete',
            'success' => $delete_result && $get_after_delete === FALSE,
            'details' => "Delete result: $delete_result, Get after delete: " . var_export($get_after_delete, true)
        ];

        // Test 6: Metadata
        $this->cache->save($key, $value, 300);
        $metadata = $this->cache->get_metadata($key);
        
        $results[] = [
            'test' => 'Get Metadata',
            'success' => is_array($metadata) && isset($metadata['expire']),
            'details' => "Metadata: " . json_encode($metadata)
        ];

        // Mostrar resultados
        $this->display_test_results($results);
    }

    /**
     * Test del Cache Service
     */
    public function test_cache_service()
    {
        echo "<h2>Test Cache Service</h2>";
        
        $results = [];
        
        // Test 1: Remember pattern
        $key = $this->cache_service->generate_key('test_function', ['param1' => 'value1']);
        $result = $this->cache_service->remember($key, function() {
            return 'Generated at ' . date('Y-m-d H:i:s');
        }, 300);
        
        // Llamar de nuevo para verificar que viene del caché
        $result2 = $this->cache_service->remember($key, function() {
            return 'Should not be called';
        }, 300);
        
        $results[] = [
            'test' => 'Remember Pattern',
            'success' => $result === $result2,
            'details' => "First: '$result', Second: '$result2'"
        ];

        // Test 2: Forget
        $forget_result = $this->cache_service->forget($key);
        $result_after_forget = $this->cache_service->remember($key, function() {
            return 'After forget at ' . date('Y-m-d H:i:s');
        }, 300);
        
        $results[] = [
            'test' => 'Forget',
            'success' => $forget_result && $result_after_forget !== $result,
            'details' => "Forget result: $forget_result, New value: '$result_after_forget'"
        ];

        $this->display_test_results($results);
    }

    /**
     * Test del CacheableTrait
     */
    public function test_cacheable_trait()
    {
        echo "<h2>Test CacheableTrait</h2>";
        
        $results = [];
        
        // Test usando el modelo de ejemplo
        $test_data = $this->Test_cache_model->get_test_data();
        $test_data2 = $this->Test_cache_model->get_test_data(); // Debería venir del caché
        
        $results[] = [
            'test' => 'CacheableTrait Basic',
            'success' => !empty($test_data) && $test_data === $test_data2,
            'details' => "Data count: " . count($test_data)
        ];

        // Test de invalidación de caché
        $version_before = $this->Test_cache_model->get_cache_version_public();
        $this->Test_cache_model->bump_cache_version_public();
        $version_after = $this->Test_cache_model->get_cache_version_public();
        
        $results[] = [
            'test' => 'Cache Version Bump',
            'success' => $version_after > $version_before,
            'details' => "Before: $version_before, After: $version_after"
        ];

        $this->display_test_results($results);
    }

    /**
     * Test del Crud Model
     */
    public function test_crud_model()
    {
        echo "<h2>Test Crud Model</h2>";
        
        $results = [];
        
        // Test básico de get_all (debería usar caché)
        $start_time = microtime(true);
        $users1 = $this->Users_model->get_all();
        $time1 = microtime(true) - $start_time;
        
        $start_time = microtime(true);
        $users2 = $this->Users_model->get_all();
        $time2 = microtime(true) - $start_time;
        
        $results[] = [
            'test' => 'Crud Model Caching',
            'success' => $users1->num_rows() === $users2->num_rows() && $time2 < $time1,
            'details' => "First query: {$time1}s, Second query: {$time2}s, Rows: " . $users1->num_rows()
        ];

        $this->display_test_results($results);
    }

    /**
     * Test de performance
     */
    public function test_performance()
    {
        echo "<h2>Test Performance</h2>";
        
        $results = [];
        $iterations = 100;
        
        // Test de escritura
        $start_time = microtime(true);
        for ($i = 0; $i < $iterations; $i++) {
            $this->cache_service->remember("perf_test_$i", function() use ($i) {
                return "Value $i";
            }, 300);
        }
        $write_time = microtime(true) - $start_time;
        
        // Test de lectura
        $start_time = microtime(true);
        for ($i = 0; $i < $iterations; $i++) {
            $this->cache_service->remember("perf_test_$i", function() use ($i) {
                return "Should not be called";
            }, 300);
        }
        $read_time = microtime(true) - $start_time;
        
        $results[] = [
            'test' => 'Performance Test',
            'success' => $read_time < $write_time,
            'details' => "Write time: {$write_time}s, Read time: {$read_time}s for $iterations operations"
        ];

        $this->display_test_results($results);
    }

    /**
     * Test de compatibilidad completa
     */
    public function test_compatibility()
    {
        echo "<h2>Test Compatibilidad Completa</h2>";
        
        $results = [];
        
        // Test 1: Verificar que Predis está activo
        $use_predis = $this->config->item('use_predis_cache');
        $results[] = [
            'test' => 'Predis Enabled',
            'success' => $use_predis === TRUE,
            'details' => "use_predis_cache: " . var_export($use_predis, true)
        ];

        // Test 2: Verificar que las librerías se cargan correctamente
        $this->load->library('Predis_cache_service');
        $predis_loaded = isset($this->predis_cache_service);
        
        $results[] = [
            'test' => 'Predis Libraries Load',
            'success' => $predis_loaded,
            'details' => "Predis service loaded: " . var_export($predis_loaded, true)
        ];

        // Test 3: Verificar estadísticas de Predis
        if ($predis_loaded) {
            $stats = $this->predis_cache_service->get_stats();
            $results[] = [
                'test' => 'Predis Connection',
                'success' => $stats['connected'] === true,
                'details' => "Stats: " . json_encode($stats)
            ];
        }

        $this->display_test_results($results);
    }

    /**
     * Obtener estadísticas de Redis
     */
    public function get_stats()
    {
        echo "<h2>Estadísticas Redis/Predis</h2>";
        
        $this->load->library('Predis_cache_service');
        $stats = $this->predis_cache_service->get_stats();
        
        echo "<pre>";
        print_r($stats);
        echo "</pre>";
        
        // También mostrar configuración actual
        echo "<h3>Configuración Actual</h3>";
        echo "<pre>";
        echo "use_predis_cache: " . var_export($this->config->item('use_predis_cache'), true) . "\n";
        echo "cache_driver: " . $this->config->item('cache_driver') . "\n";
        echo "cache_backup: " . $this->config->item('cache_backup') . "\n";
        print_r($this->config->item('redis'));
        echo "</pre>";
    }

    /**
     * Muestra los resultados de los tests
     */
    private function display_test_results($results)
    {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Test</th><th>Resultado</th><th>Detalles</th></tr>";
        
        foreach ($results as $result) {
            $status = $result['success'] ? '✅ PASS' : '❌ FAIL';
            $color = $result['success'] ? 'green' : 'red';
            
            echo "<tr>";
            echo "<td>{$result['test']}</td>";
            echo "<td style='color: $color; font-weight: bold;'>$status</td>";
            echo "<td>{$result['details']}</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        echo "<br><a href='" . site_url('cache_test') . "'>← Volver al menú</a>";
    }
}
