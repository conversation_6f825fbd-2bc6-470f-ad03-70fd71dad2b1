<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Enhanced Cache Demo Controller
 *
 * Demuestra las nuevas capacidades del sistema de caché v2.0
 * - Predis como driver por defecto
 * - Capa de abstracción con fallback automático
 * - Compatibilidad total con código existente
 * - Funcionalidad extendida para cualquier modelo
 *
 * @package    CodeIgniter
 * @subpackage Controllers
 * @category   Demo
 * <AUTHOR> Pro Team
 * @version    2.0.0
 */
class Enhanced_cache_demo extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        
        // Cargar modelos de demostración
        $this->load->model('Demo_users_model');
        $this->load->model('Demo_products_model');
        $this->load->library('Cache_service');
    }

    /**
     * Página principal de demostración
     */
    public function index()
    {
        echo "<h1>🚀 Sistema de Caché v2.0 - Demostración</h1>";
        echo "<p><strong>Predis como driver por defecto con fallback automático</strong></p>";
        echo "<hr>";
        
        echo "<h2>📋 Demostraciones Disponibles</h2>";
        echo "<ul>";
        echo "<li><a href='" . site_url('enhanced_cache_demo/demo_abstraction_layer') . "'>🔧 Capa de Abstracción</a></li>";
        echo "<li><a href='" . site_url('enhanced_cache_demo/demo_enhanced_models') . "'>📊 Modelos Mejorados</a></li>";
        echo "<li><a href='" . site_url('enhanced_cache_demo/demo_compatibility') . "'>✅ Compatibilidad con Crud_model</a></li>";
        echo "<li><a href='" . site_url('enhanced_cache_demo/demo_driver_switching') . "'>🔄 Alternancia de Drivers</a></li>";
        echo "<li><a href='" . site_url('enhanced_cache_demo/demo_proxy_class') . "'>🎭 Proxy Class para DB Results</a></li>";
        echo "<li><a href='" . site_url('enhanced_cache_demo/demo_cache_service') . "'>⚙️ Cache Service v2.0</a></li>";
        echo "</ul>";
        
        echo "<hr>";
        echo "<h2>ℹ️ Estado del Sistema</h2>";
        $this->show_system_status();
    }

    /**
     * Demuestra la capa de abstracción
     */
    public function demo_abstraction_layer()
    {
        echo "<h1>🔧 Demostración: Capa de Abstracción</h1>";
        
        // Cargar la capa de abstracción directamente
        $this->load->library('Cache_abstraction_layer');
        
        echo "<h2>Información del Driver Actual</h2>";
        $driver_info = $this->cache_abstraction_layer->get_driver_info();
        echo "<pre>" . json_encode($driver_info, JSON_PRETTY_PRINT) . "</pre>";
        
        echo "<h2>Test de Operaciones Básicas</h2>";
        $results = [];
        
        // Test 1: Save y Get
        $test_key = 'abstraction_test_' . time();
        $test_value = ['message' => 'Hello from abstraction layer!', 'timestamp' => time()];
        
        $save_result = $this->cache_abstraction_layer->save($test_key, $test_value, 300);
        $get_result = $this->cache_abstraction_layer->get($test_key);
        
        $results[] = [
            'test' => 'Save/Get Complex Data',
            'success' => $save_result && $get_result === $test_value,
            'details' => "Saved: $save_result, Retrieved: " . json_encode($get_result)
        ];
        
        // Test 2: Increment
        $counter_key = 'abstraction_counter_' . time();
        $this->cache_abstraction_layer->save($counter_key, 10, 300);
        $increment_result = $this->cache_abstraction_layer->increment($counter_key, 5);
        
        $results[] = [
            'test' => 'Increment Operation',
            'success' => $increment_result == 15,
            'details' => "Increment result: $increment_result"
        ];
        
        // Limpiar
        $this->cache_abstraction_layer->delete($test_key);
        $this->cache_abstraction_layer->delete($counter_key);
        
        $this->display_test_results($results);
        echo "<p><a href='" . site_url('enhanced_cache_demo') . "'>← Volver</a></p>";
    }

    /**
     * Demuestra los modelos mejorados
     */
    public function demo_enhanced_models()
    {
        echo "<h1>📊 Demostración: Modelos Mejorados</h1>";
        
        echo "<h2>Demo Users Model (Enhanced Base Model)</h2>";
        
        $results = [];
        
        // Test 1: Get all con caché
        $start_time = microtime(true);
        $users1 = $this->Demo_users_model->get_all();
        $time1 = microtime(true) - $start_time;
        
        $start_time = microtime(true);
        $users2 = $this->Demo_users_model->get_all();
        $time2 = microtime(true) - $start_time;
        
        $results[] = [
            'test' => 'Enhanced Model Caching',
            'success' => $users1->num_rows() === $users2->num_rows() && $time2 < $time1,
            'details' => "Primera consulta: {$time1}s, Segunda: {$time2}s, Filas: " . $users1->num_rows()
        ];
        
        // Test 2: Proxy class functionality
        $first_user = $users1->row();
        $users_array = $users1->result_array();
        
        $results[] = [
            'test' => 'Proxy Class Functionality',
            'success' => is_object($first_user) && is_array($users_array),
            'details' => "First user: " . (is_object($first_user) ? 'Object' : 'Not object') . 
                        ", Array result: " . count($users_array) . " items"
        ];
        
        // Test 3: Cache info
        $cache_info = $this->Demo_users_model->get_cache_status();
        
        $results[] = [
            'test' => 'Cache Status Info',
            'success' => isset($cache_info['driver_info']),
            'details' => "Driver: " . $cache_info['driver_info']['type'] . 
                        ", Version: " . $cache_info['current_version']
        ];
        
        $this->display_test_results($results);
        echo "<p><a href='" . site_url('enhanced_cache_demo') . "'>← Volver</a></p>";
    }

    /**
     * Demuestra compatibilidad con Crud_model
     */
    public function demo_compatibility()
    {
        echo "<h1>✅ Demostración: Compatibilidad con Crud_model</h1>";
        
        echo "<p>Verificando que los modelos existentes que extienden Crud_model siguen funcionando...</p>";
        
        $results = [];
        
        try {
            // Test con Users_model (asumiendo que existe y extiende Crud_model)
            if (class_exists('Users_model')) {
                $this->load->model('Users_model');
                
                $start_time = microtime(true);
                $users = $this->Users_model->get_all();
                $time = microtime(true) - $start_time;
                
                $results[] = [
                    'test' => 'Users_model Compatibility',
                    'success' => is_object($users) && method_exists($users, 'num_rows'),
                    'details' => "Query time: {$time}s, Rows: " . $users->num_rows()
                ];
            } else {
                $results[] = [
                    'test' => 'Users_model Compatibility',
                    'success' => true,
                    'details' => "Users_model not found (normal in demo environment)"
                ];
            }
            
            // Test del trait original
            $this->load->model('Test_cache_model');
            $test_data = $this->Test_cache_model->get_test_data();
            
            $results[] = [
                'test' => 'Original CacheableTrait',
                'success' => !empty($test_data),
                'details' => "Test data count: " . count($test_data)
            ];
            
        } catch (Exception $e) {
            $results[] = [
                'test' => 'Compatibility Test',
                'success' => false,
                'details' => "Error: " . $e->getMessage()
            ];
        }
        
        $this->display_test_results($results);
        echo "<p><a href='" . site_url('enhanced_cache_demo') . "'>← Volver</a></p>";
    }

    /**
     * Demuestra la alternancia de drivers
     */
    public function demo_driver_switching()
    {
        echo "<h1>🔄 Demostración: Alternancia de Drivers</h1>";
        
        $this->load->library('Cache_abstraction_layer');
        
        echo "<h2>Driver Actual</h2>";
        $initial_info = $this->cache_abstraction_layer->get_driver_info();
        echo "<p><strong>Driver inicial:</strong> " . $initial_info['type'] . "</p>";
        
        $results = [];
        
        // Test con driver actual
        $test_key = 'switching_test_' . time();
        $test_value = 'Test value for driver switching';
        
        $save1 = $this->cache_abstraction_layer->save($test_key, $test_value, 300);
        $get1 = $this->cache_abstraction_layer->get($test_key);
        
        $results[] = [
            'test' => "Test con {$initial_info['type']} driver",
            'success' => $save1 && $get1 === $test_value,
            'details' => "Save: $save1, Get: '$get1'"
        ];
        
        // Intentar cambiar a otro driver
        $target_driver = $initial_info['type'] === 'predis' ? 'native' : 'predis';
        $switch_result = $this->cache_abstraction_layer->switch_driver($target_driver);
        
        if ($switch_result) {
            $new_info = $this->cache_abstraction_layer->get_driver_info();
            
            $results[] = [
                'test' => "Cambio a $target_driver driver",
                'success' => $new_info['type'] === $target_driver,
                'details' => "Nuevo driver: " . $new_info['type']
            ];
            
            // Test con nuevo driver
            $test_key2 = 'switching_test2_' . time();
            $save2 = $this->cache_abstraction_layer->save($test_key2, $test_value, 300);
            $get2 = $this->cache_abstraction_layer->get($test_key2);
            
            $results[] = [
                'test' => "Test con {$new_info['type']} driver",
                'success' => $save2 && $get2 === $test_value,
                'details' => "Save: $save2, Get: '$get2'"
            ];
            
            // Limpiar
            $this->cache_abstraction_layer->delete($test_key2);
        } else {
            $results[] = [
                'test' => "Cambio a $target_driver driver",
                'success' => false,
                'details' => "No se pudo cambiar al driver $target_driver"
            ];
        }
        
        // Limpiar
        $this->cache_abstraction_layer->delete($test_key);
        
        $this->display_test_results($results);
        echo "<p><a href='" . site_url('enhanced_cache_demo') . "'>← Volver</a></p>";
    }

    /**
     * Demuestra la funcionalidad del proxy class
     */
    public function demo_proxy_class()
    {
        echo "<h1>🎭 Demostración: Proxy Class para DB Results</h1>";
        
        echo "<p>El proxy class mantiene compatibilidad total con CI_DB_result...</p>";
        
        // Crear datos de prueba
        $test_data = [
            (object)['id' => 1, 'name' => 'Usuario 1', 'email' => '<EMAIL>'],
            (object)['id' => 2, 'name' => 'Usuario 2', 'email' => '<EMAIL>'],
            (object)['id' => 3, 'name' => 'Usuario 3', 'email' => '<EMAIL>']
        ];
        
        // Usar el trait para crear el proxy
        $this->load->model('Demo_users_model');
        $proxy = $this->Demo_users_model->create_test_proxy($test_data);
        
        $results = [];
        
        // Test métodos del proxy
        $results[] = [
            'test' => 'num_rows()',
            'success' => $proxy->num_rows() === 3,
            'details' => "Returned: " . $proxy->num_rows()
        ];
        
        $results[] = [
            'test' => 'result()',
            'success' => count($proxy->result()) === 3,
            'details' => "Array count: " . count($proxy->result())
        ];
        
        $results[] = [
            'test' => 'row()',
            'success' => is_object($proxy->row()) && $proxy->row()->id === 1,
            'details' => "First row ID: " . $proxy->row()->id
        ];
        
        $results[] = [
            'test' => 'result_array()',
            'success' => is_array($proxy->result_array()) && count($proxy->result_array()) === 3,
            'details' => "Array result count: " . count($proxy->result_array())
        ];
        
        $results[] = [
            'test' => 'list_fields()',
            'success' => in_array('name', $proxy->list_fields()),
            'details' => "Fields: " . implode(', ', $proxy->list_fields())
        ];
        
        $this->display_test_results($results);
        echo "<p><a href='" . site_url('enhanced_cache_demo') . "'>← Volver</a></p>";
    }

    /**
     * Demuestra Cache Service v2.0
     */
    public function demo_cache_service()
    {
        echo "<h1>⚙️ Demostración: Cache Service v2.0</h1>";
        
        $results = [];
        
        // Test remember pattern
        $key = $this->cache_service->generate_key('demo_function', ['param' => 'value']);
        
        $start_time = microtime(true);
        $result1 = $this->cache_service->remember($key, function() {
            usleep(100000); // Simular operación lenta
            return 'Generated at ' . date('H:i:s');
        }, 300);
        $time1 = microtime(true) - $start_time;
        
        $start_time = microtime(true);
        $result2 = $this->cache_service->remember($key, function() {
            return 'Should not be called';
        }, 300);
        $time2 = microtime(true) - $start_time;
        
        $results[] = [
            'test' => 'Cache Service Remember Pattern',
            'success' => $result1 === $result2 && $time2 < $time1,
            'details' => "First: {$time1}s, Second: {$time2}s, Value: '$result1'"
        ];
        
        // Test forget
        $forget_result = $this->cache_service->forget($key);
        
        $results[] = [
            'test' => 'Cache Service Forget',
            'success' => $forget_result,
            'details' => "Forget result: $forget_result"
        ];
        
        $this->display_test_results($results);
        echo "<p><a href='" . site_url('enhanced_cache_demo') . "'>← Volver</a></p>";
    }

    /**
     * Muestra el estado del sistema
     */
    private function show_system_status()
    {
        $this->load->library('Cache_abstraction_layer');
        $driver_info = $this->cache_abstraction_layer->get_driver_info();
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Componente</th><th>Estado</th><th>Detalles</th></tr>";
        
        echo "<tr>";
        echo "<td>Driver Activo</td>";
        echo "<td style='color: green; font-weight: bold;'>✅ " . strtoupper($driver_info['type']) . "</td>";
        echo "<td>" . $driver_info['class'] . "</td>";
        echo "</tr>";
        
        echo "<tr>";
        echo "<td>Predis Disponible</td>";
        echo "<td style='color: " . (class_exists('Predis\\Client') ? 'green' : 'red') . "; font-weight: bold;'>";
        echo (class_exists('Predis\\Client') ? '✅ SÍ' : '❌ NO');
        echo "</td>";
        echo "<td>" . (class_exists('Predis\\Client') ? 'Predis\\Client cargado' : 'Predis no disponible') . "</td>";
        echo "</tr>";
        
        echo "<tr>";
        echo "<td>Fallback Habilitado</td>";
        echo "<td style='color: " . ($driver_info['config']['enable_fallback'] ? 'green' : 'orange') . "; font-weight: bold;'>";
        echo ($driver_info['config']['enable_fallback'] ? '✅ SÍ' : '⚠️ NO');
        echo "</td>";
        echo "<td>Cambio automático entre drivers</td>";
        echo "</tr>";
        
        echo "<tr>";
        echo "<td>Prioridad de Drivers</td>";
        echo "<td>📋 CONFIGURADO</td>";
        echo "<td>" . implode(' → ', $driver_info['priority']) . "</td>";
        echo "</tr>";
        
        echo "</table>";
    }

    /**
     * Muestra los resultados de los tests
     */
    private function display_test_results($results)
    {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Test</th><th>Resultado</th><th>Detalles</th></tr>";
        
        foreach ($results as $result) {
            $status = $result['success'] ? '✅ PASS' : '❌ FAIL';
            $color = $result['success'] ? 'green' : 'red';
            
            echo "<tr>";
            echo "<td>{$result['test']}</td>";
            echo "<td style='color: $color; font-weight: bold;'>$status</td>";
            echo "<td>{$result['details']}</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
}
