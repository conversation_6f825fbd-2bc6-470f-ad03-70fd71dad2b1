# Migración Redis Nativo → Predis

## Resumen

Este documento describe la migración completa del sistema de caché de Redis nativo de CodeIgniter 3 a Predis, una librería PHP más robusta y mantenida para interactuar con Redis.

## Motivación

### Problemas del Driver Nativo
- **Obsolescencia**: El driver nativo de CI3 no se actualiza y tiene limitaciones
- **Dependencias**: Requiere la extensión phpredis que puede ser problemática
- **Funcionalidad limitada**: Menos características que Predis
- **Estabilidad**: Problemas de conexión y manejo de errores

### Beneficios de Predis
- **Mantenimiento activo**: Librería activamente mantenida
- **Sin dependencias de extensiones**: Funciona con PHP puro
- **Más características**: Soporte completo para comandos Redis
- **Mejor manejo de errores**: Gestión robusta de conexiones y fallos
- **Compatibilidad**: Funciona en cualquier entorno PHP

## Arquitectura de la Migración

### Componentes Implementados

#### 1. Predis_cache_service.php
- **Propósito**: Servicio principal que encapsula Predis
- **Características**:
  - Conexión automática a Redis usando Predis
  - Manejo de serialización automática
  - Gestión de TTL flexible
  - Logging detallado de operaciones
  - Fallback en caso de errores

#### 2. Predis_cache_driver.php
- **Propósito**: Wrapper que emula la interfaz del driver CI
- **Características**:
  - Compatibilidad total con la API existente
  - Soporte para driver de respaldo (file)
  - Delegación automática de métodos
  - Manejo transparente de errores

#### 3. CacheableTrait (Modificado)
- **Cambios**:
  - Detección automática de configuración `use_predis_cache`
  - Inicialización condicional entre Predis y driver nativo
  - Mantiene compatibilidad total con código existente

#### 4. Cache_service (Modificado)
- **Cambios**:
  - Soporte para Predis mediante configuración
  - Mantiene interfaz pública sin cambios
  - Inicialización inteligente del driver

### Configuración

#### cache.php
```php
// Habilitar Predis como reemplazo del driver nativo de Redis
// TRUE = usar Predis, FALSE = usar driver nativo de CodeIgniter
$config['use_predis_cache'] = TRUE;
```

#### redis.php
```php
// Configuración compatible con ambos drivers
$config['socket_type'] = 'tcp';
$config['host'] = '127.0.0.1';
$config['password'] = 'tHjGQeXrgmI45Y';
$config['port'] = 6379;
$config['timeout'] = 0;
```

## Compatibilidad

### Modelos Afectados
- **Crud_model**: ✅ Compatible sin cambios
- **Todos los modelos que extienden Crud_model**: ✅ Compatible sin cambios
- **Modelos que usan CacheableTrait**: ✅ Compatible sin cambios

### Controladores Afectados
- **Controladores que usan Cache_service**: ✅ Compatible sin cambios
- **Controladores que usan driver CI directamente**: ✅ Compatible con adaptador

### Métodos Soportados
- `get($key)` - Obtener valor del caché
- `save($key, $value, $ttl)` - Guardar valor en caché
- `delete($key)` - Eliminar clave del caché
- `increment($key, $offset)` - Incrementar valor numérico
- `decrement($key, $offset)` - Decrementar valor numérico
- `clean()` - Limpiar todo el caché
- `get_metadata($key)` - Obtener metadatos de clave
- `exists($key)` - Verificar si clave existe

## Testing

### Controladores de Prueba

#### Cache_test
- **URL**: `http://localhost/cache_test`
- **Funciones**:
  - Tests de operaciones básicas
  - Tests de Cache_service
  - Tests de CacheableTrait
  - Tests de Crud_model
  - Tests de performance

#### Cache_migration
- **URL**: `http://localhost/cache_migration`
- **Funciones**:
  - Alternar entre Predis y driver nativo
  - Comparación de performance
  - Tests de compatibilidad
  - Información del sistema

### Script de Validación
```bash
php app/validate_migration.php
```

## Performance

### Benchmarks Típicos
- **Operaciones de escritura**: Similar o ligeramente mejor
- **Operaciones de lectura**: Comparable al driver nativo
- **Gestión de memoria**: Más eficiente en conexiones largas
- **Estabilidad**: Significativamente mejor

### Optimizaciones Implementadas
- **Conexión persistente**: Reutilización de conexiones
- **Serialización inteligente**: Solo cuando es necesario
- **Caching de metadatos**: Reducción de consultas Redis
- **Manejo de errores**: Fallback automático

## Migración Gradual

### Fase 1: Instalación (✅ Completada)
- Instalación de Predis via Composer
- Creación de nuevas librerías
- Modificación de componentes existentes

### Fase 2: Testing (✅ Completada)
- Creación de tests de compatibilidad
- Validación de funcionalidad
- Comparación de performance

### Fase 3: Activación (✅ Completada)
- Configuración de `use_predis_cache = TRUE`
- Monitoreo de funcionamiento
- Validación en producción

## Rollback

### En caso de problemas:
1. Cambiar `$config['use_predis_cache'] = FALSE;` en `cache.php`
2. El sistema volverá automáticamente al driver nativo
3. No se requieren cambios adicionales

### Verificación de rollback:
```php
// Verificar driver actual
$use_predis = $this->config->item('use_predis_cache');
echo $use_predis ? 'Usando Predis' : 'Usando driver nativo';
```

## Monitoreo

### Logs a Revisar
- `application/logs/`: Mensajes de Predis Cache
- Errores de conexión Redis
- Performance de operaciones de caché

### Métricas Importantes
- Tiempo de respuesta de operaciones de caché
- Tasa de aciertos de caché
- Errores de conexión Redis
- Uso de memoria

## Mantenimiento

### Actualizaciones de Predis
```bash
cd app
composer update predis/predis
```

### Configuración de Producción
- Ajustar timeouts según entorno
- Configurar logging apropiado
- Monitorear uso de memoria Redis
- Implementar alertas de conexión

## Conclusión

La migración a Predis proporciona:
- ✅ **Mayor estabilidad** en conexiones Redis
- ✅ **Mejor mantenimiento** a largo plazo
- ✅ **Compatibilidad total** con código existente
- ✅ **Funcionalidad extendida** para futuras mejoras
- ✅ **Independencia de extensiones** PHP específicas

El sistema está ahora preparado para un uso más robusto y escalable del caché Redis.
