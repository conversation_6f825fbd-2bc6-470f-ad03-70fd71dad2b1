<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Demo Products Model
 *
 * Otro modelo de demostración que muestra diferentes configuraciones de caché
 */
class Demo_products_model extends Enhanced_base_model
{
    public function __construct()
    {
        parent::__construct();
        
        // Configurar con diferentes opciones
        $this->use_table('products', [
            'cache_time' => 1800,       // 30 minutos
            'cache_prefix' => 'prod',   // Prefijo diferente
            'cache_enabled' => true
        ]);
    }

    /**
     * Obtiene productos por categoría
     */
    public function get_by_category($category_id)
    {
        return $this->get_where(['category_id' => $category_id, 'active' => 1]);
    }

    /**
     * Obtiene productos en oferta
     */
    public function get_on_sale()
    {
        return $this->get_where(['on_sale' => 1, 'active' => 1]);
    }

    /**
     * Búsqueda de productos con caché
     */
    public function search_products($term)
    {
        $cache_key = $this->generate_enhanced_cache_key('search_products', ['term' => $term]);
        
        return $this->get_enhanced_cached_data($cache_key, function() use ($term) {
            // Simular búsqueda
            return [
                (object)['id' => 1, 'name' => "Producto con '$term'", 'price' => 100],
                (object)['id' => 2, 'name' => "Otro producto '$term'", 'price' => 200]
            ];
        });
    }
}
