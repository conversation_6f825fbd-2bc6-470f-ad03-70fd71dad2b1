<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Predis Cache Driver Wrapper
 *
 * Wrapper que emula exactamente la interfaz del driver de caché de CodeIgniter
 * pero usando Predis internamente. Esto permite reemplazar el driver nativo
 * sin cambiar ningún código existente.
 *
 * @package    CodeIgniter
 * @subpackage Libraries
 * @category   Cache
 * <AUTHOR> Pro Team
 * @version    1.0.0
 */
class Predis_cache_driver
{
    /**
     * Instancia del servicio Predis
     * @var Predis_cache_service
     */
    protected $predis_service;

    /**
     * Instancia de CodeIgniter
     * @var CI_Controller
     */
    protected $CI;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->CI =& get_instance();
        
        // Cargar el servicio Predis
        $this->CI->load->library('Predis_cache_service');
        $this->predis_service = $this->CI->predis_cache_service;
        
        log_message('info', 'Predis Cache Driver Wrapper Initialized');
    }

    /**
     * Verifica si el driver está soportado
     *
     * @return bool
     */
    public function is_supported()
    {
        return $this->predis_service->is_supported();
    }

    /**
     * Obtiene un valor del caché
     *
     * @param string $key Clave del caché
     * @return mixed Valor del caché o FALSE si no existe
     */
    public function get($key)
    {
        return $this->predis_service->get($key);
    }

    /**
     * Guarda un valor en el caché
     *
     * @param string $key Clave del caché
     * @param mixed $value Valor a guardar
     * @param int $ttl Tiempo de vida en segundos
     * @param bool $raw No usado, mantenido por compatibilidad
     * @return bool TRUE en éxito, FALSE en fallo
     */
    public function save($key, $value, $ttl = 60, $raw = FALSE)
    {
        return $this->predis_service->save($key, $value, $ttl);
    }

    /**
     * Elimina una clave del caché
     *
     * @param string $key Clave a eliminar
     * @return bool TRUE en éxito, FALSE en fallo
     */
    public function delete($key)
    {
        return $this->predis_service->delete($key);
    }

    /**
     * Incrementa un valor numérico en el caché
     *
     * @param string $key Clave a incrementar
     * @param int $offset Cantidad a incrementar
     * @return mixed Nuevo valor o FALSE en fallo
     */
    public function increment($key, $offset = 1)
    {
        return $this->predis_service->increment($key, $offset);
    }

    /**
     * Decrementa un valor numérico en el caché
     *
     * @param string $key Clave a decrementar
     * @param int $offset Cantidad a decrementar
     * @return mixed Nuevo valor o FALSE en fallo
     */
    public function decrement($key, $offset = 1)
    {
        return $this->predis_service->decrement($key, $offset);
    }

    /**
     * Limpia todo el caché
     *
     * @return bool TRUE en éxito, FALSE en fallo
     */
    public function clean()
    {
        return $this->predis_service->clean();
    }

    /**
     * Obtiene información de una clave de caché
     *
     * @param string $key Clave del caché
     * @return array|FALSE Metadatos o FALSE si no existe
     */
    public function get_metadata($key)
    {
        return $this->predis_service->get_metadata($key);
    }

    /**
     * Verifica si una clave existe en el caché
     *
     * @param string $key Clave a verificar
     * @return bool TRUE si existe, FALSE si no
     */
    public function exists($key)
    {
        return $this->predis_service->exists($key);
    }

    /**
     * Obtiene el cliente Predis para operaciones avanzadas
     *
     * @return Predis\Client|null
     */
    public function get_predis_client()
    {
        return $this->predis_service->get_predis_client();
    }

    /**
     * Obtiene estadísticas de conexión
     *
     * @return array
     */
    public function get_stats()
    {
        return $this->predis_service->get_stats();
    }

    /**
     * Método mágico para delegar llamadas no definidas al servicio Predis
     *
     * @param string $method Nombre del método
     * @param array $args Argumentos del método
     * @return mixed
     */
    public function __call($method, $args)
    {
        if (method_exists($this->predis_service, $method)) {
            return call_user_func_array(array($this->predis_service, $method), $args);
        }
        
        log_message('error', "Predis Cache Driver: Method '{$method}' not found");
        return FALSE;
    }
}

/**
 * Clase que emula el comportamiento del driver de caché de CI
 * Mantiene compatibilidad con el patrón de uso existente
 */
class Predis_cache_adapter
{
    /**
     * Driver Predis
     * @var Predis_cache_driver
     */
    protected $predis_driver;

    /**
     * Driver de respaldo (file)
     * @var CI_Cache_file
     */
    protected $backup_driver;

    /**
     * Configuración
     * @var array
     */
    protected $config;

    /**
     * Constructor
     *
     * @param array $config Configuración del caché
     */
    public function __construct($config = array())
    {
        $this->config = $config;
        $CI =& get_instance();
        
        // Inicializar driver principal (Predis)
        $CI->load->library('Predis_cache_driver');
        $this->predis_driver = $CI->predis_cache_driver;
        
        // Inicializar driver de respaldo si está configurado
        if (isset($config['backup']) && $config['backup'] === 'file') {
            $CI->load->driver('cache');
            $this->backup_driver = $CI->cache->file;
        }
        
        log_message('info', 'Predis Cache Adapter Initialized');
    }

    /**
     * Obtiene un valor del caché
     *
     * @param string $key Clave del caché
     * @return mixed Valor del caché o FALSE si no existe
     */
    public function get($key)
    {
        // Intentar con driver principal
        if ($this->predis_driver->is_supported()) {
            $result = $this->predis_driver->get($key);
            if ($result !== FALSE) {
                return $result;
            }
        }
        
        // Fallback al driver de respaldo
        if ($this->backup_driver) {
            return $this->backup_driver->get($key);
        }
        
        return FALSE;
    }

    /**
     * Guarda un valor en el caché
     *
     * @param string $key Clave del caché
     * @param mixed $value Valor a guardar
     * @param int $ttl Tiempo de vida en segundos
     * @return bool TRUE en éxito, FALSE en fallo
     */
    public function save($key, $value, $ttl = 60)
    {
        $success = FALSE;
        
        // Intentar con driver principal
        if ($this->predis_driver->is_supported()) {
            $success = $this->predis_driver->save($key, $value, $ttl);
        }
        
        // También guardar en respaldo si está configurado
        if ($this->backup_driver) {
            $this->backup_driver->save($key, $value, $ttl);
        }
        
        return $success;
    }

    /**
     * Elimina una clave del caché
     *
     * @param string $key Clave a eliminar
     * @return bool TRUE en éxito, FALSE en fallo
     */
    public function delete($key)
    {
        $success = FALSE;
        
        // Eliminar del driver principal
        if ($this->predis_driver->is_supported()) {
            $success = $this->predis_driver->delete($key);
        }
        
        // También eliminar del respaldo
        if ($this->backup_driver) {
            $this->backup_driver->delete($key);
        }
        
        return $success;
    }

    /**
     * Incrementa un valor numérico en el caché
     *
     * @param string $key Clave a incrementar
     * @param int $offset Cantidad a incrementar
     * @return mixed Nuevo valor o FALSE en fallo
     */
    public function increment($key, $offset = 1)
    {
        if ($this->predis_driver->is_supported()) {
            return $this->predis_driver->increment($key, $offset);
        }
        
        if ($this->backup_driver) {
            return $this->backup_driver->increment($key, $offset);
        }
        
        return FALSE;
    }

    /**
     * Decrementa un valor numérico en el caché
     *
     * @param string $key Clave a decrementar
     * @param int $offset Cantidad a decrementar
     * @return mixed Nuevo valor o FALSE en fallo
     */
    public function decrement($key, $offset = 1)
    {
        if ($this->predis_driver->is_supported()) {
            return $this->predis_driver->decrement($key, $offset);
        }
        
        if ($this->backup_driver) {
            return $this->backup_driver->decrement($key, $offset);
        }
        
        return FALSE;
    }

    /**
     * Limpia todo el caché
     *
     * @return bool TRUE en éxito, FALSE en fallo
     */
    public function clean()
    {
        $success = FALSE;
        
        if ($this->predis_driver->is_supported()) {
            $success = $this->predis_driver->clean();
        }
        
        if ($this->backup_driver) {
            $this->backup_driver->clean();
        }
        
        return $success;
    }

    /**
     * Obtiene información de una clave de caché
     *
     * @param string $key Clave del caché
     * @return array|FALSE Metadatos o FALSE si no existe
     */
    public function get_metadata($key)
    {
        if ($this->predis_driver->is_supported()) {
            return $this->predis_driver->get_metadata($key);
        }
        
        if ($this->backup_driver) {
            return $this->backup_driver->get_metadata($key);
        }
        
        return FALSE;
    }

    /**
     * Delega métodos no definidos al driver principal
     */
    public function __call($method, $args)
    {
        if ($this->predis_driver->is_supported()) {
            return call_user_func_array(array($this->predis_driver, $method), $args);
        }
        
        if ($this->backup_driver && method_exists($this->backup_driver, $method)) {
            return call_user_func_array(array($this->backup_driver, $method), $args);
        }
        
        return FALSE;
    }
}
