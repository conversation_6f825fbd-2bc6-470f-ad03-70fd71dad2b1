<?php
/**
 * Script de validación de migración Redis → Predis
 * 
 * Este script valida que la migración se haya completado correctamente
 * y que todos los componentes funcionen como se espera.
 */

// Configurar entorno
define('ENVIRONMENT', 'development');
define('BASEPATH', __DIR__ . '/system/');
define('APPPATH', __DIR__ . '/application/');
define('FCPATH', __DIR__ . '/');

// Cargar autoloader de Composer
require_once __DIR__ . '/vendor/autoload.php';

// Cargar configuración básica
require_once APPPATH . 'config/config.php';
require_once APPPATH . 'config/cache.php';
require_once APPPATH . 'config/redis.php';

echo "🔍 VALIDACIÓN DE MIGRACIÓN REDIS → PREDIS\n";
echo "==========================================\n\n";

$errors = [];
$warnings = [];
$success_count = 0;
$total_tests = 0;

/**
 * Función helper para mostrar resultados
 */
function test_result($name, $success, $details = '') {
    global $success_count, $total_tests, $errors;
    
    $total_tests++;
    if ($success) {
        $success_count++;
        echo "✅ $name\n";
        if ($details) echo "   $details\n";
    } else {
        $errors[] = $name;
        echo "❌ $name\n";
        if ($details) echo "   ERROR: $details\n";
    }
    echo "\n";
}

// Test 1: Verificar archivos de migración
echo "📁 VERIFICANDO ARCHIVOS DE MIGRACIÓN\n";
echo "------------------------------------\n";

$required_files = [
    'application/libraries/Predis_cache_service.php',
    'application/libraries/Predis_cache_driver.php',
    'application/traits/CacheableTrait.php',
    'application/libraries/Cache_service.php',
    'application/controllers/Cache_test.php',
    'application/controllers/Cache_migration.php',
    'application/models/Test_cache_model.php'
];

foreach ($required_files as $file) {
    $exists = file_exists(APPPATH . '../' . $file);
    test_result("Archivo $file", $exists, $exists ? 'Existe' : 'No encontrado');
}

// Test 2: Verificar dependencias
echo "📦 VERIFICANDO DEPENDENCIAS\n";
echo "---------------------------\n";

test_result('Predis disponible', class_exists('Predis\\Client'), 
    class_exists('Predis\\Client') ? 'Predis\\Client cargado' : 'Predis no disponible');

test_result('Autoloader Composer', file_exists(__DIR__ . '/vendor/autoload.php'),
    'vendor/autoload.php encontrado');

// Test 3: Verificar configuración
echo "⚙️ VERIFICANDO CONFIGURACIÓN\n";
echo "----------------------------\n";

$use_predis = isset($config['use_predis_cache']) ? $config['use_predis_cache'] : false;
test_result('Configuración use_predis_cache', isset($config['use_predis_cache']),
    "use_predis_cache = " . var_export($use_predis, true));

$cache_driver = isset($config['cache_driver']) ? $config['cache_driver'] : 'unknown';
test_result('Cache driver configurado', $cache_driver === 'redis',
    "cache_driver = $cache_driver");

$redis_config = isset($config['redis']) ? $config['redis'] : [];
test_result('Configuración Redis', !empty($redis_config),
    'Configuración Redis: ' . json_encode($redis_config));

// Test 4: Probar conexión Predis
echo "🔌 PROBANDO CONEXIÓN PREDIS\n";
echo "---------------------------\n";

try {
    $predis_config = [
        'scheme' => isset($redis_config['socket_type']) ? $redis_config['socket_type'] : 'tcp',
        'host' => isset($redis_config['host']) ? $redis_config['host'] : '127.0.0.1',
        'port' => isset($redis_config['port']) ? $redis_config['port'] : 6379,
        'password' => isset($redis_config['password']) ? $redis_config['password'] : null,
        'timeout' => isset($redis_config['timeout']) ? $redis_config['timeout'] : 0
    ];
    
    $client = new Predis\Client($predis_config);
    $ping_result = $client->ping();
    test_result('Conexión Predis', $ping_result === 'PONG', "Ping: $ping_result");
    
    // Test operaciones básicas
    $test_key = 'validation_test_' . time();
    $test_value = 'Migration validation test';
    
    $client->set($test_key, $test_value);
    $retrieved = $client->get($test_key);
    $client->del($test_key);
    
    test_result('Operaciones básicas Predis', $retrieved === $test_value,
        "Set/Get/Delete funcionando");
    
} catch (Exception $e) {
    test_result('Conexión Predis', false, $e->getMessage());
}

// Test 5: Verificar clases de migración
echo "🏗️ VERIFICANDO CLASES DE MIGRACIÓN\n";
echo "----------------------------------\n";

// Simular carga de clases CodeIgniter
if (!function_exists('get_instance')) {
    function get_instance() {
        static $CI;
        if (!$CI) {
            $CI = new stdClass();
            $CI->config = new stdClass();
            $CI->config->item = function($item) use ($config) {
                return isset($config[$item]) ? $config[$item] : false;
            };
        }
        return $CI;
    }
}

if (!function_exists('log_message')) {
    function log_message($level, $message) {
        // Función stub para logging
    }
}

// Cargar trait
if (file_exists(APPPATH . 'traits/CacheableTrait.php')) {
    require_once APPPATH . 'traits/CacheableTrait.php';
    test_result('CacheableTrait cargado', trait_exists('CacheableTrait'),
        'Trait disponible para modelos');
}

// Test 6: Verificar estructura de archivos modificados
echo "📝 VERIFICANDO MODIFICACIONES\n";
echo "-----------------------------\n";

// Verificar que CacheableTrait tiene las modificaciones
$trait_content = file_get_contents(APPPATH . 'traits/CacheableTrait.php');
$has_predis_support = strpos($trait_content, 'use_predis_cache') !== false;
test_result('CacheableTrait modificado', $has_predis_support,
    'Soporte para Predis añadido al trait');

// Verificar que Cache_service tiene las modificaciones
$cache_service_content = file_get_contents(APPPATH . 'libraries/Cache_service.php');
$has_predis_support = strpos($cache_service_content, 'Predis_cache_adapter') !== false;
test_result('Cache_service modificado', $has_predis_support,
    'Soporte para Predis añadido a Cache_service');

// Verificar configuración de cache.php
$cache_config_content = file_get_contents(APPPATH . 'config/cache.php');
$has_predis_config = strpos($cache_config_content, 'use_predis_cache') !== false;
test_result('Configuración cache.php actualizada', $has_predis_config,
    'Configuración use_predis_cache añadida');

// Test 7: Verificar compatibilidad hacia atrás
echo "🔄 VERIFICANDO COMPATIBILIDAD\n";
echo "-----------------------------\n";

// Verificar que los métodos principales están disponibles
$predis_service_content = file_get_contents(APPPATH . 'libraries/Predis_cache_service.php');
$required_methods = ['get', 'save', 'delete', 'increment', 'clean', 'get_metadata'];
$methods_found = 0;

foreach ($required_methods as $method) {
    if (strpos($predis_service_content, "function $method(") !== false) {
        $methods_found++;
    }
}

test_result('Métodos de compatibilidad', $methods_found === count($required_methods),
    "$methods_found/" . count($required_methods) . " métodos encontrados");

// Resumen final
echo "📊 RESUMEN DE VALIDACIÓN\n";
echo "========================\n";

$success_rate = ($success_count / $total_tests) * 100;

echo "Tests ejecutados: $total_tests\n";
echo "Tests exitosos: $success_count\n";
echo "Tests fallidos: " . count($errors) . "\n";
echo "Tasa de éxito: " . round($success_rate, 2) . "%\n\n";

if (count($errors) > 0) {
    echo "❌ ERRORES ENCONTRADOS:\n";
    foreach ($errors as $error) {
        echo "   - $error\n";
    }
    echo "\n";
}

if ($success_rate >= 90) {
    echo "🎉 MIGRACIÓN COMPLETADA EXITOSAMENTE\n";
    echo "La migración de Redis nativo a Predis se ha completado correctamente.\n";
    echo "El sistema está listo para usar Predis como driver de caché.\n\n";
    
    echo "📋 PRÓXIMOS PASOS:\n";
    echo "1. Acceder a http://localhost/cache_migration para gestionar la migración\n";
    echo "2. Ejecutar tests completos en http://localhost/cache_test\n";
    echo "3. Monitorear logs de aplicación para verificar funcionamiento\n";
    echo "4. Realizar pruebas en entorno de producción\n";
    
} else {
    echo "⚠️ MIGRACIÓN INCOMPLETA\n";
    echo "Se encontraron errores que deben ser corregidos antes de usar Predis.\n";
    echo "Revisar los errores listados arriba y corregir los problemas.\n";
}

echo "\n";
echo "🔗 ENLACES ÚTILES:\n";
echo "- Panel de migración: http://localhost/cache_migration\n";
echo "- Tests completos: http://localhost/cache_test\n";
echo "- Documentación Predis: https://github.com/predis/predis\n";
echo "\n";
