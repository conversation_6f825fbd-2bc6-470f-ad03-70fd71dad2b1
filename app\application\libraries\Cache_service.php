<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Cache Service Library
 *
 * Servicio de caché optimizado y compatible con CodeIgniter 3
 * VERSIÓN 2.0: Utiliza la capa de abstracción con Predis como driver por defecto
 * Mantiene compatibilidad total con código existente
 */
class Cache_service {
    protected $CI;
    protected $cache_time = 12 * 60 * 60; // Cache duration in seconds
    protected $prefix = '';

    public function __construct() {
        $this->CI =& get_instance();
        $this->init_cache();
        log_message('info', 'Cache Service v2.0 Class Initialized with abstraction layer');
    }

    /**
     * Inicializa el sistema de cache
     * VERSIÓN 2.0: Usa la capa de abstracción con Predis por defecto
     */
    protected function init_cache(): void
    {
        // Solo cargar si no está ya cargado
        if (!isset($this->CI->cache)) {
            // Cargar la capa de abstracción de caché
            $this->CI->load->library('Cache_abstraction_layer');
            $this->CI->cache = $this->CI->cache_abstraction_layer;
        }
    }

    /**
     * Genera una clave de cache consistente
     */
    public function generate_key(string $function_name, array $params = []): string
    {
        ksort($params); // Ordenar parámetros para consistencia
        $params_str = json_encode($params);
        $hashed_params = md5($params_str);
        return "{$this->prefix}:{$function_name}:{$hashed_params}";
    }

    /**
     * Obtiene o genera datos cacheados
     *
     * @param string $key Clave de caché
     * @param callable $callback Función que genera los datos si no están en caché
     * @param int|null $ttl Tiempo de vida en segundos
     * @return mixed
     */
    public function remember(string $key, callable $callback, ?int $ttl = null)
    {
        try {
            // Intentar obtener del caché
            $data = $this->CI->cache->get($key);

            if ($data !== FALSE) {
                log_message('info', "Cache hit for key: {$key}");
                return $data;
            }

            // Si no está en caché, generar datos
            $data = $callback();

            // Guardar en caché
            $success = $this->CI->cache->save(
                $key,
                $data,
                $ttl ?? $this->cache_time
            );

            if (!$success) {
                log_message('error', "Failed to save data to cache for key: {$key}");
            } else {
                log_message('info', "Data cached successfully for key: {$key}");
            }

            return $data;

        } catch (Exception $e) {
            log_message('error', "Cache error for key {$key}: " . $e->getMessage());
            return $callback(); // En caso de error, ejecutar el callback directamente
        }
    }

    /**
     * Elimina una clave del caché
     */
    public function forget(string $key): bool
    {
        $result = $this->CI->cache->delete($key);
        if ($result) {
            log_message('info', "Cache key deleted: {$key}");
        } else {
            log_message('error', "Failed to delete cache key: {$key}");
        }
        return $result;
    }

    /**
     * Limpia todo el caché
     */
    public function flush(): bool
    {
        $result = $this->CI->cache->clean();
        if ($result) {
            log_message('info', "Cache flushed successfully");
        } else {
            log_message('error', "Failed to flush cache");
        }
        return $result;
    }

    /**
     * Obtiene metadatos de una clave de caché
     */
    public function get_metadata(string $key): ?array
    {
        return $this->CI->cache->get_metadata($key);
    }
}